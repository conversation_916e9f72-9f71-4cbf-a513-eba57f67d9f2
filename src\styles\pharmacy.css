/* Animations pour la page Pharmacy */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Classes d'animation */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-pulse-subtle {
  animation: pulse 2s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Styles pour les cartes de pharmacie */
.pharmacy-card {
  transition: all 0.3s ease;
}

.pharmacy-card:hover {
  transform: translateY(-5px);
}

/* Styles pour les badges de garde */
.duty-badge {
  position: relative;
  overflow: hidden;
}

.duty-badge::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: shine 2s infinite;
}

@keyframes shine {
  to {
    left: 100%;
  }
}

/* Styles pour les onglets */
.custom-tabs {
  position: relative;
}

.custom-tabs::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: rgba(59, 130, 246, 0.1);
}

/* Styles pour le fond de la page */
.pharmacy-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Styles pour les boutons */
.btn-primary {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transition: all 0.5s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

/* Styles pour la pagination */
.pagination-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 2rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.pagination-button:hover {
  transform: translateY(-2px);
}

.pagination-button.active {
  background-color: var(--meddoc-primary);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

.pagination-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  color: #6b7280;
}

.pagination-info {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

/* Animation pour les boutons de pagination */
@keyframes pulse-subtle {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.2);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.pagination-button.active {
  animation: pulse-subtle 2s infinite;
}

/* Styles pour les états vides */
.empty-state {
  transition: all 0.3s ease;
}

.empty-state:hover {
  transform: scale(1.02);
}

/* Styles pour la barre de recherche */
.search-bar-container {
  position: relative;
}

.search-bar-container::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 16px;
  background: linear-gradient(45deg, #3b82f6, #60a5fa);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.search-bar-container:focus-within::before {
  opacity: 0.5;
}

/* Styles pour les filtres */
.filter-panel {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.filter-panel.open {
  max-height: 500px;
  opacity: 1;
}

.filter-panel.closed {
  max-height: 0;
  opacity: 0;
  padding: 0;
  margin: 0;
}

.filter-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.filter-button::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--meddoc-primary);
  transition: width 0.3s ease;
}

.filter-button:hover::after,
.filter-button.active::after {
  width: 100%;
}

.filter-badge {
  display: inline-flex;
  align-items: center;
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--meddoc-primary);
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s ease;
}

.filter-badge:hover {
  background-color: rgba(59, 130, 246, 0.2);
}

.filter-badge .remove-icon {
  margin-left: 0.5rem;
  width: 1rem;
  height: 1rem;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.filter-badge:hover .remove-icon {
  opacity: 1;
}

/* Animation pour l'apparition des filtres */
@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.filter-panel {
  animation: slideDown 0.3s ease-out forwards;
}

/* Styles pour les sélecteurs de filtre */
.filter-select {
  appearance: none;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #374151;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  transition: all 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: var(--meddoc-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Styles pour les options de sélecteur */
select option {
  color: #1e293b; /* text-slate-900 */
  background-color: white;
  font-size: 0.875rem;
}

/* Styles pour les sélecteurs en général */
select {
  color: #1e293b !important; /* Force la couleur du texte */
  font-weight: 500;
}

/* Force la couleur du texte pour tous les éléments d'option */
option {
  color: #1e293b !important; /* text-slate-900 */
  background-color: white !important;
}

/* Styles pour les statistiques */
.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0) 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.stat-card:hover::after {
  opacity: 1;
}
