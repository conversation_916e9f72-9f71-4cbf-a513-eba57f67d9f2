import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import PharmacyForm from "./PharmacyForm";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { usePharmacyRedux } from "@/hooks/use-pharmacy-redux";
import { validateForm } from "@/utils/validateForm";

type NewPharmacyData = Omit<Pharmacy, "id">;

interface AddPharmacyProps {
  onSubmit: (data: Pharmacy, file?: File) => void; // Fonction de soumission
  pharmacy?: Pharmacy; // Données de la pharmacie (en cas d'édition)
  isEdit?: boolean; // Indique si c'est une édition ou une création
  isAddDialogOpen: boolean;
  setIsAddDialogOpen: (value: boolean) => void;
}

export default function AddPharmacy({
  onSubmit,
  pharmacy,
  isEdit = false,
  isAddDialogOpen,
  setIsAddDialogOpen,
}: AddPharmacyProps) {
  const {
    isAddPharmacyOpen,
    showAddPharmacyModal,
    handleAddPharmacy,
    isLoading,
    error,
  } = usePharmacyRedux();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  // Initialiser les états avec des valeurs par défaut
  // Utiliser un type plus flexible pour formData pour éviter les erreurs de type
  const [formData, setFormData] = useState<Omit<Pharmacy, 'contacts' | 'horaires'> & {
    contacts: { numero: string; id_pharmacie?: number; id?: number }[];
    horaires: PharmacySchedule[];
  }>({
    id: pharmacy?.id,
    nom_pharmacie: pharmacy?.nom_pharmacie || "",
    photo_profil: pharmacy?.photo_profil || "",
    address: pharmacy?.address || "",
    province: pharmacy?.province || "",
    region: pharmacy?.region || "",
    district: pharmacy?.district || "",
    commune: pharmacy?.commune || "",
    service: pharmacy?.service || "",
    contacts: [],
    horaires: [],
  });

  // États pour les contacts, horaires et erreurs
  // Utiliser un type plus flexible pour les contacts pour éviter les erreurs de type
  const [contacts, setContacts] = useState<{ numero: string; id_pharmacie?: number; id?: number }[]>([{ numero: "" }]);
  const [horaires, setHoraires] = useState<PharmacySchedule[]>([{ heure_debut: "", heure_fin: "" }]);

  // Mettre à jour les états lorsque la prop pharmacy change
  useEffect(() => {
    if (pharmacy) {
      console.log("Mise à jour des données avec la pharmacie:", pharmacy);

      // Mettre à jour les états avec des objets complètement nouveaux
      setFormData({
        id: pharmacy.id,
        nom_pharmacie: pharmacy.nom_pharmacie || "",
        photo_profil: pharmacy.photo_profil || "",
        address: pharmacy.address || "",
        province: pharmacy.province || "",
        region: pharmacy.region || "",
        district: pharmacy.district || "",
        commune: pharmacy.commune || "",
        service: pharmacy.service || "",
        contacts: pharmacy.contacts || [],
        horaires: pharmacy.horaires || [],
      });

      // Utiliser des copies pour éviter les références partagées
    }
  }, [pharmacy]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Références pour le champ de fichier et le sélecteur de localisation
  const fileInputRef = useRef<HTMLInputElement>(null);
  const locationSelectorRef = useRef<LocationSelectorRef>(null);

  // Gestion des changements de localisation
  const handleLocationChange = (location: {
    province: string;
    region?: string;
    district?: string;
    commune: string;
  }) => {
    setFormData((prev) => ({
      ...prev,
      province: location.province,
      region: location.region || "",
      district: location.district || "",
      commune: location.commune,
    }));
  };

  // Gestion des changements des services
  const handleServiceChange = (val: string) => {
    setFormData((prev) => ({
      ...prev,
      service: val,
    }));
  };

  // Soumission du formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Utiliser une version modifiée de validateForm qui accepte nos types flexibles
   
    if (validateForm(formData, contacts, horaires, setErrors)) {
      // Filtrer les horaires vides
      const filteredHoraires = horaires.filter(
        h => h.heure_debut && h.heure_fin
      );

      // S'assurer que les contacts et horaires n'ont pas d'ID
      // Filtrer les contacts vides pour éviter les problèmes
      const cleanContacts = contacts
        .filter(contact => contact.numero.trim() !== "")
        .map(contact => ({
          numero: contact.numero
          // Ne pas ajouter id_pharmacie ici, le service s'en chargera
        }));

      console.log("Contacts nettoyés pour soumission:", cleanContacts);

      const cleanHoraires = filteredHoraires.map(horaire => ({
        heure_debut: horaire.heure_debut,
        heure_fin: horaire.heure_fin,
        jour: horaire.jour
        // Ne pas ajouter id_pharmacie ici, le service s'en chargera
      }));

      // Préparer les données finales
      const finalData = {
        ...formData,
        contacts: cleanContacts,
        horaires: cleanHoraires,
      } as Pharmacy; // Forcer le type Pharmacy pour éviter les erreurs

      // Appeler la fonction de soumission
      try {
        onSubmit(
          finalData,
          selectedFile || undefined
        );
      } catch (error) {
        console.error("Erreur lors de la soumission:", error);
      }
    }
  };

  // Gestion des changements du fichier photo
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      setFormData((prev) => ({
        ...prev,
        photo_profil: URL.createObjectURL(file),
      }));
    }
  };

  // Gestion des contacts (ajout, suppression, mise à jour)
  const addContact = () => {
    // Créer un nouvel objet contact sans référence aux objets existants
    const newContact = {
      numero: "",
      id_pharmacie: formData.id || undefined
    };
    setContacts([...contacts, newContact]);
  };

  const removeContact = (index: number) => {
    // Créer une nouvelle liste sans le contact à supprimer
    const newContacts = contacts.filter((_, i) => i !== index);
    setContacts(newContacts);
  };

  const updateContact = (index: number, numero: string) => {
    // Créer une copie complète de la liste des contacts
    const newContacts = [...contacts];
    // Créer un nouvel objet contact sans référence à l'objet existant
    newContacts[index] = {
      numero,
      id_pharmacie: formData.id || undefined
    };
    setContacts(newContacts);
  };

  // Gestion des horaires (ajout, suppression, mise à jour)
  const addHoraire = () => {
    // Créer un nouvel objet horaire sans référence aux objets existants
    const newHoraire = { heure_debut: "", heure_fin: "" };
    setHoraires([...horaires, newHoraire]);
  };

  const removeHoraire = (index: number) => {
    // Créer une nouvelle liste sans l'horaire à supprimer
    const newHoraires = horaires.filter((_, i) => i !== index);
    setHoraires(newHoraires);
  };

  const updateHoraire = (
    index: number,
    field: keyof PharmacySchedule,
    value: string
  ) => {
    // Créer une copie complète de la liste des horaires
    const newHoraires = [...horaires];
    // Créer un nouvel objet horaire sans référence à l'objet existant
    newHoraires[index] = { ...{}, ...newHoraires[index], [field]: value };
    setHoraires(newHoraires);
  };
  // Gestionnaire d'événements pour l'ouverture/fermeture du modal
  const handleOpenChange = (open: boolean) => {
    setIsAddDialogOpen(open);

    // Si le modal est fermé, réinitialiser les données après un court délai
    if (!open) {
      console.log("Modal fermé, réinitialisation des données...");
      // Attendre que l'animation de fermeture soit terminée
      setTimeout(() => {
        if (pharmacy) {
          console.log("Réinitialisation des données avec la pharmacie:", pharmacy);

          // Nettoyer les contacts et horaires en créant de nouveaux objets
          // pour éviter tout problème de référence
          const cleanContacts = pharmacy.contacts?.map(contact => ({
            numero: contact.numero,
            id_pharmacie: pharmacy.id || undefined
          })) || [{ numero: "", id_pharmacie: pharmacy.id || undefined }];

          const cleanHoraires = pharmacy.horaires?.map(horaire => ({
            heure_debut: horaire.heure_debut,
            heure_fin: horaire.heure_fin,
            jour: horaire.jour,
            id_pharmacie: pharmacy.id || undefined
          })) || [{ heure_debut: "", heure_fin: "", id_pharmacie: pharmacy.id || undefined }];

          console.log("Contacts nettoyés:", cleanContacts);
          console.log("Horaires nettoyés:", cleanHoraires);

          // Mettre à jour les états avec des objets complètement nouveaux
          setFormData({
            id: pharmacy.id,
            nom_pharmacie: pharmacy.nom_pharmacie || "",
            photo_profil: pharmacy.photo_profil || "",
            address: pharmacy.address || "",
            province: pharmacy.province || "",
            region: pharmacy.region || "",
            district: pharmacy.district || "",
            commune: pharmacy.commune || "",
            service: pharmacy.service || "",
            contacts: [...cleanContacts],
            horaires: [...cleanHoraires],
          });

          // Utiliser des copies pour éviter les références partagées
          setContacts([...cleanContacts]);
          setHoraires([...cleanHoraires]);
        }
      }, 300);
    }
  };

  return (
    <Dialog open={isAddDialogOpen} onOpenChange={handleOpenChange}>
      {!isEdit && (
        <DialogTrigger asChild>
          <Button className="w-full md:w-auto">Ajouter une pharmacie</Button>
        </DialogTrigger>
      )}
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEdit ? "Modifier la pharmacie" : "Ajouter une nouvelle pharmacie"}</DialogTitle>
        </DialogHeader>
        <PharmacyForm
          handleSubmit={handleSubmit}
          formData={formData}
          setFormData={setFormData}
          errors={errors}
          fileInputRef={fileInputRef}
          handleFileChange={handleFileChange}
          locationSelectorRef={locationSelectorRef}
          handleLocationChange={handleLocationChange}
          handleServiceChange={handleServiceChange}
          contacts={contacts}
          updateContact={updateContact}
          removeContact={removeContact}
          addContact={addContact}
          horaires={horaires}
          updateHoraire={updateHoraire}
          removeHoraire={removeHoraire}
          addHoraire={addHoraire}
          pharmacy={pharmacy}
          isEdit={isEdit}
        />
      </DialogContent>
    </Dialog>
  );
}
