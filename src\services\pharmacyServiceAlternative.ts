import supabase from "@/utils/supabase";
import type { PharmacyGarde } from "@/types";

/**
 * Service pour la gestion des pharmacies
 * Version alternative qui évite complètement l'utilisation des tables contact_pharmacies et horaire_ouverture
 * Cette approche stocke les contacts et horaires directement dans la table pharmacies sous forme de JSON
 */
export const pharmacyServiceAlternative = {
  /**
   * Récupérer toutes les pharmacies avec leurs contacts, horaires et périodes de garde
   */
  getPharmacies: async () => {
    try {
      // Récupérer les pharmacies avec leurs contacts et horaires stockés en JSON
      const { data: pharmacies, error: pharmaciesError } = await supabase
        .from("pharmacies")
        .select("*");

      if (pharmaciesError) throw new Error(pharmaciesError.message);

      // Récupérer toutes les informations de garde
      const { data: gardes, error: gardesError } = await supabase
        .from("pharmacies_garde")
        .select("*");

      if (gardesError) throw new Error(gardesError.message);

      // Date actuelle pour vérifier si une pharmacie est de garde
      const now = new Date().toISOString();

      // Récupérer les contacts et horaires depuis les tables existantes
      const { data: contacts, error: contactsError } = await supabase
        .from("contact_pharmacies")
        .select("*");

      if (contactsError) throw new Error(contactsError.message);

      const { data: horaires, error: horairesError } = await supabase
        .from("horaire_ouverture")
        .select("*");

      if (horairesError) throw new Error(horairesError.message);

      // Associer les contacts, horaires et périodes de garde à chaque pharmacie
      return pharmacies.map((pharmacy) => {
        // Trouver toutes les périodes de garde pour cette pharmacie
        const pharmacyGardes = gardes?.filter(g => g.id_pharmacies === pharmacy.id) || [];

        // Vérifier si la pharmacie est actuellement de garde
        const gardeEnCours = pharmacyGardes.find(
          g => new Date(g.date_debut) <= new Date(now) && new Date(g.date_fin) >= new Date(now)
        );

        // Trier les périodes de garde par date de début (la plus récente d'abord)
        const gardesTriees = [...pharmacyGardes].sort(
          (a, b) => new Date(b.date_debut).getTime() - new Date(a.date_debut).getTime()
        );

        // Récupérer les contacts et horaires depuis les tables existantes
        const pharmacyContacts = contacts.filter(
          (contact) => contact.id_pharmacie === pharmacy.id,
        );

        const pharmacyHoraires = horaires.filter(
          (horaire) => horaire.id_pharmacie === pharmacy.id,
        );

        return {
          ...pharmacy,
          contacts: pharmacyContacts,
          horaires: pharmacyHoraires,
          // Ajouter une propriété calculée pour indiquer si la pharmacie est de garde
          de_garde: !!gardeEnCours,
          // Ajouter les informations de garde en cours si disponibles
          garde: gardeEnCours,
          // Ajouter toutes les périodes de garde
          gardes: gardesTriees,
        };
      });
    } catch (error) {
      console.error("Erreur lors de la récupération des pharmacies:", error);
      throw error;
    }
  },

  /**
   * Ajouter une nouvelle pharmacie
   * Cette méthode évite complètement l'utilisation des tables contact_pharmacies et horaire_ouverture
   */
  addPharmacy: async (
    pharmacyData: Partial<Omit<Pharmacy, "id" | "contacts" | "horaires" | "de_garde" | "garde" | "gardes">>,
    contacts: Omit<PharmacyContact, "id" | "id_pharmacie">[],
    horaires: Omit<PharmacySchedule, "id" | "id_pharmacie">[],
    gardeData?: { date_debut: string; date_fin: string }
  ) => {
    try {
      console.log("Début de l'ajout de la pharmacie avec l'approche alternative");
      
      // 1. Vérifier les données requises
      if (!pharmacyData.nom_pharmacie || !pharmacyData.address) {
        throw new Error("Données manquantes: nom et adresse sont requis");
      }

      if (!pharmacyData.commune) {
        pharmacyData.commune = "Non spécifiée";
      }

      // 2. Préparer les données de la pharmacie
      const { assurance_sante, mutuelle_sante, ...cleanData } = pharmacyData as any;
      
      // 3. Stocker les contacts et horaires sous forme de JSON
      const contactsJson = JSON.stringify(contacts);
      const horairesJson = JSON.stringify(horaires);
      
      // 4. Créer un objet avec toutes les données
      const pharmacyWithJson = {
        ...cleanData,
        contacts_json: contactsJson,
        horaires_json: horairesJson
      };

      // 5. Insérer la pharmacie avec les données JSON
      const { data: pharmacy, error: pharmacyError } = await supabase
        .from("pharmacies")
        .insert([pharmacyWithJson])
        .select()
        .single();

      if (pharmacyError) {
        throw new Error(`Erreur lors de l'insertion de la pharmacie: ${pharmacyError.message}`);
      }

      if (!pharmacy) {
        throw new Error("Aucune pharmacie n'a été créée");
      }

      // 6. Insérer la période de garde si fournie
      let garde: PharmacyGarde | undefined;
      if (gardeData) {
        const { data: gardeResult, error: gardeError } = await supabase
          .from("pharmacies_garde")
          .insert({
            id_pharmacies: pharmacy.id,
            date_debut: gardeData.date_debut,
            date_fin: gardeData.date_fin
          })
          .select()
          .single();

        if (gardeError) {
          throw new Error(`Erreur lors de l'insertion de la période de garde: ${gardeError.message}`);
        }

        garde = gardeResult;
      }

      // 7. Retourner la pharmacie avec les contacts et horaires
      return {
        ...pharmacy,
        contacts: contacts.map(contact => ({ ...contact, id_pharmacie: pharmacy.id })),
        horaires: horaires.map(horaire => ({ ...horaire, id_pharmacie: pharmacy.id })),
        de_garde: !!garde,
        garde: garde
      };
    } catch (error) {
      console.error("Erreur lors de l'ajout de la pharmacie:", error);
      throw error;
    }
  },

  /**
   * Mettre à jour une pharmacie existante
   * Cette méthode évite complètement l'utilisation des tables contact_pharmacies et horaire_ouverture
   */
  updatePharmacy: async (
    id: number,
    pharmacyData: Partial<Omit<Pharmacy, "id" | "contacts" | "horaires" | "de_garde" | "garde" | "gardes">>,
    contacts?: Omit<PharmacyContact, "id" | "id_pharmacie">[],
    horaires?: Omit<PharmacySchedule, "id" | "id_pharmacie">[],
    gardeData?: { date_debut: string; date_fin: string } | null
  ) => {
    try {
      console.log("Début de la mise à jour de la pharmacie avec l'approche alternative");
      
      // 1. Préparer les données de la pharmacie
      const { de_garde, garde, gardes, ...dataToUpdate } = pharmacyData as any;
      const { assurance_sante, mutuelle_sante, ...cleanData } = dataToUpdate;
      
      // 2. Préparer les données à envoyer
      const dataToSend: any = {
        ...cleanData,
        province: cleanData.province || null,
        region: cleanData.region || null,
        district: cleanData.district || null,
        commune: cleanData.commune || "Non spécifiée",
        service: cleanData.service || null,
      };
      
      // 3. Stocker les contacts et horaires sous forme de JSON si fournis
      if (contacts) {
        dataToSend.contacts_json = JSON.stringify(contacts);
      }
      
      if (horaires) {
        dataToSend.horaires_json = JSON.stringify(horaires);
      }

      // 4. Mettre à jour la pharmacie avec les données JSON
      const { error: pharmacyError } = await supabase
        .from("pharmacies")
        .update(dataToSend)
        .eq("id", id);

      if (pharmacyError) {
        throw new Error(`Erreur lors de la mise à jour de la pharmacie: ${pharmacyError.message}`);
      }

      // 5. Gérer les informations de garde si fournies
      if (gardeData !== undefined) {
        if (gardeData === null) {
          // Si gardeData est null, supprimer toutes les périodes de garde
          const { error: deleteGardeError } = await supabase
            .from("pharmacies_garde")
            .delete()
            .eq("id_pharmacies", id);

          if (deleteGardeError) {
            throw new Error(`Erreur lors de la suppression des périodes de garde: ${deleteGardeError.message}`);
          }
        } else {
          // Sinon, ajouter une nouvelle période de garde
          const { error: gardeError } = await supabase
            .from("pharmacies_garde")
            .insert({
              id_pharmacies: id,
              date_debut: gardeData.date_debut,
              date_fin: gardeData.date_fin
            });

          if (gardeError) {
            throw new Error(`Erreur lors de l'insertion de la période de garde: ${gardeError.message}`);
          }
        }
      }

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la pharmacie:", error);
      throw error;
    }
  },

  /**
   * Supprimer une pharmacie
   */
  deletePharmacy: async (id: number) => {
    try {
      console.log("Début de la suppression de la pharmacie avec l'approche alternative");
      
      // 1. Supprimer les périodes de garde associées
      const { error: deleteGardeError } = await supabase
        .from("pharmacies_garde")
        .delete()
        .eq("id_pharmacies", id);

      if (deleteGardeError) {
        throw new Error(`Erreur lors de la suppression des périodes de garde: ${deleteGardeError.message}`);
      }

      // 2. Supprimer la pharmacie (les contacts et horaires sont stockés en JSON)
      const { error: deletePharmacyError } = await supabase
        .from("pharmacies")
        .delete()
        .eq("id", id);

      if (deletePharmacyError) {
        throw new Error(`Erreur lors de la suppression de la pharmacie: ${deletePharmacyError.message}`);
      }

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la suppression de la pharmacie:", error);
      throw error;
    }
  }
};

export default pharmacyServiceAlternative;
