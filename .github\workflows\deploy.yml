name: Deploy React App to VPS

on:
  push:
    branches:
      - main

jobs:
  deploy:
    name: Deploy to VPS via SSH & Docker
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          # Créer la clé SSH avec un format correct
          echo "${{ secrets.VPS_SSH_KEY }}" | base64 -d > ~/.ssh/id_rsa || echo "${{ secrets.VPS_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          chmod 700 ~/.ssh
          # Vérifier le format de la clé
          ssh-keygen -l -f ~/.ssh/id_rsa
          # Ajouter l'hôte aux known_hosts
          ssh-keyscan -H ${{ secrets.VPS_HOST }} >> ~/.ssh/known_hosts
          # Test de connexion
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} "echo 'SSH connection test successful'"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build React App
        run: |
          # Créer le fichier .env avec les variables d'environnement
          cat > .env << EOF
          VITE_SUPABASE_URL=${{ secrets.VITE_SUPABASE_URL }}
          VITE_SUPABASE_ANON_KEY=${{ secrets.VITE_SUPABASE_ANON_KEY }}
          EOF

          # Vérifier le contenu du fichier .env
          echo "Contenu du fichier .env:"
          cat .env

          # Build de l'application
          npm run build

      - name: Prepare VPS directory
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          echo "Current user: $(whoami)"
          echo "Home directory: $HOME"
          echo "Current directory: $(pwd)"
          mkdir -p $HOME/meddoc-app
          ls -la $HOME/
          echo "Directory created successfully"
          EOF

      - name: Copy files to VPS
        run: |
          # Créer un répertoire temporaire
          mkdir -p temp_deploy

          # Copier TOUT le contenu du dépôt (sauf ce qu'on veut exclure)
          cp -r . temp_deploy/

          # Supprimer ce qu'on ne veut pas transférer
          rm -rf temp_deploy/.git temp_deploy/node_modules temp_deploy/dist

          # Vérifier que index.html est bien présent
          echo "Vérification du fichier index.html:"
          ls -la temp_deploy/public/index.html || echo "ERREUR: index.html non trouvé"

          # Créer l'archive
          tar -czf deploy.tar.gz -C temp_deploy .

          # Transférer l'archive
          scp -i ~/.ssh/id_rsa deploy.tar.gz ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }}:~/

          # Extraire sur le VPS
          ssh -i ~/.ssh/id_rsa ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          rm -rf ~/meddoc-app
          mkdir -p ~/meddoc-app
          tar -xzf ~/deploy.tar.gz -C ~/meddoc-app
          rm ~/deploy.tar.gz
          echo "Contenu du dossier après extraction:"
          ls -la ~/meddoc-app/public
          EOF

          # Nettoyer localement
          rm -rf temp_deploy deploy.tar.gz

      - name: Build and deploy on VPS
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          cd ~/meddoc-app
          docker compose down || true
          docker compose build
          docker compose up -d
          docker compose ps
          EOF

