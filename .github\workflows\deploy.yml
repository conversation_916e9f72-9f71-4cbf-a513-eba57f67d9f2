name: Deploy React App to VPS

on:
  push:
    branches:
      - develop

jobs:
  deploy:
    name: Deploy to VPS via SSH & Docker
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          # C<PERSON>er la clé SSH avec un format correct
          echo "${{ secrets.VPS_SSH_KEY }}" | base64 -d > ~/.ssh/id_rsa || echo "${{ secrets.VPS_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          chmod 700 ~/.ssh
          # Vérifier le format de la clé
          ssh-keygen -l -f ~/.ssh/id_rsa
          # Ajouter l'hôte aux known_hosts
          ssh-keyscan -H ${{ secrets.VPS_HOST }} >> ~/.ssh/known_hosts
          # Test de connexion
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} "echo 'SSH connection test successful'"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build React App
        run: |
          echo "VITE_SUPABASE_URL=${{ secrets.VITE_SUPABASE_URL }}" >> .env
          echo "VITE_SUPABASE_ANON_KEY=${{ secrets.VITE_SUPABASE_ANON_KEY }}" >> .env
          npm run build

      - name: Copy files to VPS
        run: |
          rsync -avz --delete \
            -e "ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -o ConnectTimeout=10" \
            --exclude='.git' \
            --exclude='node_modules' \
            --exclude='.env.local' \
            --exclude='dist' \
            ./ ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }}:/home/<USER>/meddoc-app

      - name: Build and deploy on VPS
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          cd ~/meddoc-app
          docker compose down || true
          docker compose build
          docker compose up -d
          docker compose ps
          EOF

