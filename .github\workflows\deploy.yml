name: Deploy React App to VPS

on:
  push:
    branches:
      - main

jobs:
  deploy:
    name: Deploy to VPS via SSH & Docker
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Prepare SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_KEY }}" | tr -d '\r' > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          chmod 700 ~/.ssh
          ssh-keyscan -H ${{ secrets.VPS_HOST }} >> ~/.ssh/known_hosts
          # Test SSH connection
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} "echo 'SSH connection successful'"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build React App
        run: |
          echo "VITE_SUPABASE_URL=${{ secrets.VITE_SUPABASE_URL }}" >> .env
          echo "VITE_SUPABASE_ANON_KEY=${{ secrets.VITE_SUPABASE_ANON_KEY }}" >> .env
          npm run build

      - name: Copy files to VPS
        run: |
          rsync -avz --delete \
            -e "ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no" \
            --exclude='.git' \
            --exclude='node_modules' \
            --exclude='.env.local' \
            ./ ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }}:/home/<USER>/meddoc-app

      - name: Build and deploy on VPS
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          cd ~/meddoc-app
          docker compose down || true
          docker compose build
          docker compose up -d
          docker compose ps
          EOF

