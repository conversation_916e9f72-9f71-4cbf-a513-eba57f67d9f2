name: Deploy React App to VPS

on:
  push:
    branches:
      - main

jobs:
  deploy:
    name: Deploy to VPS via SSH & Docker
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Prepare SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Copy files to VPS
        run: |
          rsync -avz -e "ssh -i ~/.ssh/id_rsa" ./ ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }}:/home/<USER>/meddoc-app

      - name: Build and deploy on VPS
        run: |
          ssh -i ~/.ssh/id_rsa ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          cd ~/meddoc-app
          docker compose down || true
          docker compose build
          docker compose up -d
          EOF

