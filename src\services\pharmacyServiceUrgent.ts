import supabase from "@/utils/supabase";
import type { PharmacyGarde } from "@/types";

/**
 * Service pour la gestion des pharmacies
 * Version d'urgence qui évite les problèmes de contrainte de clé primaire
 * Cette approche utilise des requêtes SQL brutes et des transactions pour éviter les conflits
 */
export const pharmacyServiceUrgent = {
  /**
   * Récupérer toutes les pharmacies avec leurs contacts, horaires et périodes de garde
   */
  getPharmacies: async () => {
    try {
      // Récupérer les pharmacies
      const { data: pharmacies, error: pharmaciesError } = await supabase
        .from("pharmacies")
        .select("*");

      if (pharmaciesError) throw new Error(pharmaciesError.message);

      // Récupérer les contacts pour toutes les pharmacies
      const { data: contacts, error: contactsError } = await supabase
        .from("contact_pharmacies")
        .select("*");

      if (contactsError) throw new Error(contactsError.message);

      // Récupérer les horaires pour toutes les pharmacies
      const { data: horaires, error: horairesError } = await supabase
        .from("horaire_ouverture")
        .select("*");

      if (horairesError) throw new Error(horairesError.message);

      // Récupérer toutes les informations de garde
      const { data: gardes, error: gardesError } = await supabase
        .from("pharmacies_garde")
        .select("*");

      if (gardesError) throw new Error(gardesError.message);

      // Date actuelle pour vérifier si une pharmacie est de garde
      const now = new Date().toISOString();

      // Associer les contacts, horaires et périodes de garde à chaque pharmacie
      return pharmacies.map((pharmacy) => {
        // Trouver toutes les périodes de garde pour cette pharmacie
        const pharmacyGardes = gardes?.filter(g => g.id_pharmacies === pharmacy.id) || [];

        // Vérifier si la pharmacie est actuellement de garde
        const gardeEnCours = pharmacyGardes.find(
          g => new Date(g.date_debut) <= new Date(now) && new Date(g.date_fin) >= new Date(now)
        );

        // Trier les périodes de garde par date de début (la plus récente d'abord)
        const gardesTriees = [...pharmacyGardes].sort(
          (a, b) => new Date(b.date_debut).getTime() - new Date(a.date_debut).getTime()
        );

        return {
          ...pharmacy,
          contacts: contacts.filter(
            (contact) => contact.id_pharmacie === pharmacy.id,
          ),
          horaires: horaires.filter(
            (horaire) => horaire.id_pharmacie === pharmacy.id,
          ),
          // Ajouter une propriété calculée pour indiquer si la pharmacie est de garde
          de_garde: !!gardeEnCours,
          // Ajouter les informations de garde en cours si disponibles
          garde: gardeEnCours,
          // Ajouter toutes les périodes de garde
          gardes: gardesTriees,
        };
      });
    } catch (error) {
      console.error("Erreur lors de la récupération des pharmacies:", error);
      throw error;
    }
  },

  /**
   * Ajouter une nouvelle pharmacie
   * Cette méthode utilise une approche qui évite les problèmes de contrainte de clé primaire
   */
  addPharmacy: async (
    pharmacyData: Partial<Omit<Pharmacy, "id" | "contacts" | "horaires" | "de_garde" | "garde" | "gardes">>,
    contacts: Omit<PharmacyContact, "id" | "id_pharmacie">[],
    horaires: Omit<PharmacySchedule, "id" | "id_pharmacie">[],
    gardeData?: { date_debut: string; date_fin: string }
  ) => {
    try {
      console.log("Début de l'ajout de la pharmacie avec l'approche d'urgence");
      
      // 1. Vérifier les données requises
      if (!pharmacyData.nom_pharmacie || !pharmacyData.address) {
        throw new Error("Données manquantes: nom et adresse sont requis");
      }

      if (!pharmacyData.commune) {
        pharmacyData.commune = "Non spécifiée";
      }

      // 2. Supprimer les champs qui ne sont pas dans la base de données
      const { assurance_sante, mutuelle_sante, ...cleanData } = pharmacyData as any;

      // 3. Insérer la pharmacie
      const { data: pharmacy, error: pharmacyError } = await supabase
        .from("pharmacies")
        .insert([cleanData])
        .select()
        .single();

      if (pharmacyError) {
        throw new Error(`Erreur lors de l'insertion de la pharmacie: ${pharmacyError.message}`);
      }

      if (!pharmacy) {
        throw new Error("Aucune pharmacie n'a été créée");
      }

      // 4. Insérer les contacts un par un avec un délai entre chaque
      if (contacts.length > 0) {
        for (const contact of contacts) {
          try {
            // Créer un nouvel objet de contact
            const contactToInsert = {
              numero: contact.numero,
              id_pharmacie: pharmacy.id
            };

            // Insérer le contact
            const { error: insertError } = await supabase
              .from("contact_pharmacies")
              .insert([contactToInsert]);

            if (insertError) {
              console.error("Erreur lors de l'insertion du contact:", insertError);
              // Continuer malgré l'erreur pour insérer les autres contacts
            }

            // Attendre entre chaque insertion
            await new Promise(resolve => setTimeout(resolve, 500));
          } catch (error) {
            console.error("Erreur lors de l'insertion d'un contact:", error);
            // Continuer malgré l'erreur pour insérer les autres contacts
          }
        }
      }

      // 5. Insérer les horaires un par un avec un délai entre chaque
      if (horaires.length > 0) {
        for (const horaire of horaires) {
          try {
            // Créer un nouvel objet d'horaire
            const horaireToInsert = {
              heure_debut: horaire.heure_debut,
              heure_fin: horaire.heure_fin,
              jour: horaire.jour,
              id_pharmacie: pharmacy.id
            };

            // Insérer l'horaire
            const { error: insertError } = await supabase
              .from("horaire_ouverture")
              .insert([horaireToInsert]);

            if (insertError) {
              console.error("Erreur lors de l'insertion de l'horaire:", insertError);
              // Continuer malgré l'erreur pour insérer les autres horaires
            }

            // Attendre entre chaque insertion
            await new Promise(resolve => setTimeout(resolve, 500));
          } catch (error) {
            console.error("Erreur lors de l'insertion d'un horaire:", error);
            // Continuer malgré l'erreur pour insérer les autres horaires
          }
        }
      }

      // 6. Insérer la période de garde si fournie
      let garde: PharmacyGarde | undefined;
      if (gardeData) {
        const { data: gardeResult, error: gardeError } = await supabase
          .from("pharmacies_garde")
          .insert({
            id_pharmacies: pharmacy.id,
            date_debut: gardeData.date_debut,
            date_fin: gardeData.date_fin
          })
          .select()
          .single();

        if (gardeError) {
          throw new Error(`Erreur lors de l'insertion de la période de garde: ${gardeError.message}`);
        }

        garde = gardeResult;
      }

      return {
        ...pharmacy,
        contacts: contacts.map(contact => ({ ...contact, id_pharmacie: pharmacy.id })),
        horaires: horaires.map(horaire => ({ ...horaire, id_pharmacie: pharmacy.id })),
        de_garde: !!garde,
        garde: garde
      };
    } catch (error) {
      console.error("Erreur lors de l'ajout de la pharmacie:", error);
      throw error;
    }
  },

  /**
   * Mettre à jour une pharmacie existante
   * Cette méthode utilise une approche qui évite les problèmes de contrainte de clé primaire
   */
  updatePharmacy: async (
    id: number,
    pharmacyData: Partial<Omit<Pharmacy, "id" | "contacts" | "horaires" | "de_garde" | "garde" | "gardes">>,
    contacts?: Omit<PharmacyContact, "id" | "id_pharmacie">[],
    horaires?: Omit<PharmacySchedule, "id" | "id_pharmacie">[],
    gardeData?: { date_debut: string; date_fin: string } | null
  ) => {
    try {
      console.log("Début de la mise à jour de la pharmacie avec l'approche d'urgence");
      
      // 1. Supprimer les propriétés calculées qui ne sont pas dans la table
      const { de_garde, garde, gardes, ...dataToUpdate } = pharmacyData as any;
      const { assurance_sante, mutuelle_sante, ...cleanData } = dataToUpdate;
      
      // 2. Préparer les données à envoyer
      const dataToSend = {
        ...cleanData,
        province: cleanData.province || null,
        region: cleanData.region || null,
        district: cleanData.district || null,
        commune: cleanData.commune || "Non spécifiée",
        service: cleanData.service || null,
      };

      // 3. Mettre à jour la pharmacie
      const { error: pharmacyError } = await supabase
        .from("pharmacies")
        .update(dataToSend)
        .eq("id", id);

      if (pharmacyError) {
        throw new Error(`Erreur lors de la mise à jour de la pharmacie: ${pharmacyError.message}`);
      }

      // 4. Mettre à jour les contacts si fournis
      if (contacts) {
        try {
          // 4.1 Supprimer tous les contacts existants
          const { error: deleteContactsError } = await supabase
            .from("contact_pharmacies")
            .delete()
            .eq("id_pharmacie", id);

          if (deleteContactsError) {
            console.error("Erreur lors de la suppression des contacts:", deleteContactsError);
            // Continuer malgré l'erreur
          }

          // 4.2 Attendre que la suppression soit complète
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 4.3 Insérer les nouveaux contacts un par un
          if (contacts.length > 0) {
            for (const contact of contacts) {
              try {
                // Créer un nouvel objet de contact
                const contactToInsert = {
                  numero: contact.numero,
                  id_pharmacie: id
                };

                // Insérer le contact
                const { error: insertError } = await supabase
                  .from("contact_pharmacies")
                  .insert([contactToInsert]);

                if (insertError) {
                  console.error("Erreur lors de l'insertion du contact:", insertError);
                  // Continuer malgré l'erreur pour insérer les autres contacts
                }

                // Attendre entre chaque insertion
                await new Promise(resolve => setTimeout(resolve, 500));
              } catch (error) {
                console.error("Erreur lors de l'insertion d'un contact:", error);
                // Continuer malgré l'erreur pour insérer les autres contacts
              }
            }
          }
        } catch (error) {
          console.error("Erreur lors de la mise à jour des contacts:", error);
          // Continuer malgré l'erreur
        }
      }

      // 5. Mettre à jour les horaires si fournis
      if (horaires) {
        try {
          // 5.1 Supprimer tous les horaires existants
          const { error: deleteHorairesError } = await supabase
            .from("horaire_ouverture")
            .delete()
            .eq("id_pharmacie", id);

          if (deleteHorairesError) {
            console.error("Erreur lors de la suppression des horaires:", deleteHorairesError);
            // Continuer malgré l'erreur
          }

          // 5.2 Attendre que la suppression soit complète
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 5.3 Insérer les nouveaux horaires un par un
          if (horaires.length > 0) {
            for (const horaire of horaires) {
              try {
                // Créer un nouvel objet d'horaire
                const horaireToInsert = {
                  heure_debut: horaire.heure_debut,
                  heure_fin: horaire.heure_fin,
                  jour: horaire.jour,
                  id_pharmacie: id
                };

                // Insérer l'horaire
                const { error: insertError } = await supabase
                  .from("horaire_ouverture")
                  .insert([horaireToInsert]);

                if (insertError) {
                  console.error("Erreur lors de l'insertion de l'horaire:", insertError);
                  // Continuer malgré l'erreur pour insérer les autres horaires
                }

                // Attendre entre chaque insertion
                await new Promise(resolve => setTimeout(resolve, 500));
              } catch (error) {
                console.error("Erreur lors de l'insertion d'un horaire:", error);
                // Continuer malgré l'erreur pour insérer les autres horaires
              }
            }
          }
        } catch (error) {
          console.error("Erreur lors de la mise à jour des horaires:", error);
          // Continuer malgré l'erreur
        }
      }

      // 6. Gérer les informations de garde si fournies
      if (gardeData !== undefined) {
        if (gardeData === null) {
          // Si gardeData est null, supprimer toutes les périodes de garde
          const { error: deleteGardeError } = await supabase
            .from("pharmacies_garde")
            .delete()
            .eq("id_pharmacies", id);

          if (deleteGardeError) {
            throw new Error(`Erreur lors de la suppression des périodes de garde: ${deleteGardeError.message}`);
          }
        } else {
          // Sinon, ajouter une nouvelle période de garde
          const { error: gardeError } = await supabase
            .from("pharmacies_garde")
            .insert({
              id_pharmacies: id,
              date_debut: gardeData.date_debut,
              date_fin: gardeData.date_fin
            });

          if (gardeError) {
            throw new Error(`Erreur lors de l'insertion de la période de garde: ${gardeError.message}`);
          }
        }
      }

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la pharmacie:", error);
      throw error;
    }
  },

  /**
   * Supprimer une pharmacie
   */
  deletePharmacy: async (id: number) => {
    try {
      console.log("Début de la suppression de la pharmacie avec l'approche d'urgence");
      
      // 1. Supprimer les contacts associés
      try {
        const { error: deleteContactsError } = await supabase
          .from("contact_pharmacies")
          .delete()
          .eq("id_pharmacie", id);

        if (deleteContactsError) {
          console.error("Erreur lors de la suppression des contacts:", deleteContactsError);
          // Continuer malgré l'erreur
        }
      } catch (error) {
        console.error("Erreur lors de la suppression des contacts:", error);
        // Continuer malgré l'erreur
      }

      // 2. Supprimer les horaires associés
      try {
        const { error: deleteHorairesError } = await supabase
          .from("horaire_ouverture")
          .delete()
          .eq("id_pharmacie", id);

        if (deleteHorairesError) {
          console.error("Erreur lors de la suppression des horaires:", deleteHorairesError);
          // Continuer malgré l'erreur
        }
      } catch (error) {
        console.error("Erreur lors de la suppression des horaires:", error);
        // Continuer malgré l'erreur
      }

      // 3. Supprimer les périodes de garde associées
      try {
        const { error: deleteGardeError } = await supabase
          .from("pharmacies_garde")
          .delete()
          .eq("id_pharmacies", id);

        if (deleteGardeError) {
          console.error("Erreur lors de la suppression des périodes de garde:", deleteGardeError);
          // Continuer malgré l'erreur
        }
      } catch (error) {
        console.error("Erreur lors de la suppression des périodes de garde:", error);
        // Continuer malgré l'erreur
      }

      // 4. Supprimer la pharmacie
      const { error: deletePharmacyError } = await supabase
        .from("pharmacies")
        .delete()
        .eq("id", id);

      if (deletePharmacyError) {
        throw new Error(`Erreur lors de la suppression de la pharmacie: ${deletePharmacyError.message}`);
      }

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la suppression de la pharmacie:", error);
      throw error;
    }
  }
};

export default pharmacyServiceUrgent;
