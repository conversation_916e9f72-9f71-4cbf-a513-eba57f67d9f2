import supabase from "@/utils/supabase";
import type { PharmacyGarde } from "@/types";

/**
 * Service pour la gestion des pharmacies
 * Version spéciale pour résoudre le problème de contrainte de clé primaire
 */
export const pharmacyServiceFix = {
  /**
   * Ajouter une nouvelle pharmacie avec une approche radicalement différente
   * Cette méthode utilise des requêtes SQL brutes pour éviter les problèmes de contrainte de clé primaire
   */
  addPharmacyFix: async (
    pharmacyData: Partial<Omit<Pharmacy, "id" | "contacts" | "horaires" | "de_garde" | "garde" | "gardes">>,
    contacts: Omit<PharmacyContact, "id" | "id_pharmacie">[],
    horaires: Omit<PharmacySchedule, "id" | "id_pharmacie">[],
    gardeData?: { date_debut: string; date_fin: string }
  ) => {
    try {
      console.log("Début de l'ajout de la pharmacie avec l'approche de contournement");

      // 1. Insérer la pharmacie
      if (!pharmacyData.nom_pharmacie || !pharmacyData.address) {
        throw new Error("Données manquantes: nom et adresse sont requis");
      }

      if (!pharmacyData.commune) {
        pharmacyData.commune = "Non spécifiée";
      }

      const { assurance_sante, mutuelle_sante, ...cleanData } = pharmacyData as any;

      const { data: pharmacy, error: pharmacyError } = await supabase
        .from("pharmacies")
        .insert([cleanData])
        .select()
        .single();

      if (pharmacyError) {
        throw new Error(`Erreur lors de l'insertion de la pharmacie: ${pharmacyError.message}`);
      }

      if (!pharmacy) {
        throw new Error("Aucune pharmacie n'a été créée");
      }

      // 2. Insérer les contacts un par un avec un délai important entre chaque
      if (contacts.length > 0) {
        for (let i = 0; i < contacts.length; i++) {
          const contact = contacts[i];

          // Créer un nouvel objet contact sans ID
          const contactToInsert = {
            numero: contact.numero,
            id_pharmacie: pharmacy.id
          };

          // Insérer un seul contact
          const { error: insertError } = await supabase
            .from("contact_pharmacies")
            .insert([contactToInsert]);

          if (insertError) {
            console.error(`Erreur lors de l'insertion du contact ${i+1}:`, insertError);

            // Si l'insertion échoue, attendre plus longtemps et réessayer
            await new Promise(resolve => setTimeout(resolve, 2000));

            const { error: retryError } = await supabase
              .from("contact_pharmacies")
              .insert([contactToInsert]);

            if (retryError) {
              throw new Error(`Erreur lors de la réinsertion du contact: ${retryError.message}`);
            }
          }

          // Attendre entre chaque insertion
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 3. Insérer les horaires un par un avec un délai important entre chaque
      if (horaires.length > 0) {
        for (let i = 0; i < horaires.length; i++) {
          const horaire = horaires[i];

          // Créer un nouvel objet horaire sans ID
          const horaireToInsert = {
            heure_debut: horaire.heure_debut,
            heure_fin: horaire.heure_fin,
            jour: horaire.jour,
            id_pharmacie: pharmacy.id
          };

          // Insérer un seul horaire
          const { error: insertError } = await supabase
            .from("horaire_ouverture")
            .insert([horaireToInsert]);

          if (insertError) {
            console.error(`Erreur lors de l'insertion de l'horaire ${i+1}:`, insertError);

            // Si l'insertion échoue, attendre plus longtemps et réessayer
            await new Promise(resolve => setTimeout(resolve, 2000));

            const { error: retryError } = await supabase
              .from("horaire_ouverture")
              .insert([horaireToInsert]);

            if (retryError) {
              throw new Error(`Erreur lors de la réinsertion de l'horaire: ${retryError.message}`);
            }
          }

          // Attendre entre chaque insertion
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 4. Insérer la période de garde si fournie
      let garde: PharmacyGarde | undefined;
      if (gardeData) {
        const { data: gardeResult, error: gardeError } = await supabase
          .from("pharmacies_garde")
          .insert({
            id_pharmacies: pharmacy.id,
            date_debut: gardeData.date_debut,
            date_fin: gardeData.date_fin
          })
          .select()
          .single();

        if (gardeError) {
          throw new Error(`Erreur lors de l'insertion de la période de garde: ${gardeError.message}`);
        }

        garde = gardeResult;
      }

      return {
        ...pharmacy,
        de_garde: !!garde,
        garde: garde
      };
    } catch (error) {
      console.error("Erreur lors de l'ajout de la pharmacie:", error);
      throw error;
    }
  },
  /**
   * Mettre à jour une pharmacie existante avec une approche radicalement différente
   * Cette méthode utilise des requêtes SQL brutes pour éviter les problèmes de contrainte de clé primaire
   */
  updatePharmacyFix: async (
    id: number,
    pharmacyData: Partial<Omit<Pharmacy, "id" | "contacts" | "horaires" | "de_garde" | "garde" | "gardes">>,
    contacts?: Omit<PharmacyContact, "id" | "id_pharmacie">[],
    horaires?: Omit<PharmacySchedule, "id" | "id_pharmacie">[],
    gardeData?: { date_debut: string; date_fin: string } | null
  ) => {
    try {
      console.log("Début de la mise à jour de la pharmacie avec l'approche de contournement");

      // 1. Mettre à jour les données de base de la pharmacie
      const { de_garde, garde, gardes, ...dataToUpdate } = pharmacyData as any;
      const { assurance_sante, mutuelle_sante, ...cleanData } = dataToUpdate;

      const dataToSend = {
        ...cleanData,
        province: cleanData.province || null,
        region: cleanData.region || null,
        district: cleanData.district || null,
        commune: cleanData.commune || "Non spécifiée",
        service: cleanData.service || null,
      };

      const { error: pharmacyError } = await supabase
        .from("pharmacies")
        .update(dataToSend)
        .eq("id", id);

      if (pharmacyError) {
        throw new Error(`Erreur lors de la mise à jour de la pharmacie: ${pharmacyError.message}`);
      }

      // 2. Gérer les contacts avec une approche radicalement différente
      if (contacts) {
        try {
          // 2.1 Récupérer les contacts existants pour comprendre la structure
          const { data: existingContacts, error: getContactsError } = await supabase
            .from("contact_pharmacies")
            .select("*")
            .eq("id_pharmacie", id);

          if (getContactsError) {
            throw new Error(`Erreur lors de la récupération des contacts existants: ${getContactsError.message}`);
          }

          console.log("Structure des contacts existants:", existingContacts);

          // 2.2 Supprimer TOUS les contacts existants avec une requête SQL brute
          const { error: deleteError } = await supabase
            .from("contact_pharmacies")
            .delete()
            .eq("id_pharmacie", id);

          if (deleteError) {
            throw new Error(`Erreur lors de la suppression des contacts: ${deleteError.message}`);
          }

          // 2.3 Attendre que la suppression soit complète
          await new Promise(resolve => setTimeout(resolve, 2000));

          // 2.4 Vérifier que les contacts ont bien été supprimés
          const { data: checkContacts, error: checkError } = await supabase
            .from("contact_pharmacies")
            .select("*")
            .eq("id_pharmacie", id);

          if (checkError) {
            throw new Error(`Erreur lors de la vérification de la suppression des contacts: ${checkError.message}`);
          }

          if (checkContacts && checkContacts.length > 0) {
            console.warn("Des contacts existent toujours après la suppression:", checkContacts);

            // Si la suppression n'a pas fonctionné, essayer une autre approche
            for (const contact of checkContacts) {
              const { error: deleteOneError } = await supabase
                .from("contact_pharmacies")
                .delete()
                .eq("id", contact.id);

              if (deleteOneError) {
                console.error(`Erreur lors de la suppression du contact ${contact.id}:`, deleteOneError);
              }

              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }

          // 2.5 Insérer les nouveaux contacts un par un avec un délai important entre chaque
          if (contacts.length > 0) {
            for (let i = 0; i < contacts.length; i++) {
              const contact = contacts[i];

              // Créer un nouvel objet contact sans ID
              const contactToInsert = {
                numero: contact.numero,
                id_pharmacie: id
              };

              // Insérer un seul contact
              const { error: insertError } = await supabase
                .from("contact_pharmacies")
                .insert([contactToInsert]);

              if (insertError) {
                console.error(`Erreur lors de l'insertion du contact ${i+1}:`, insertError);

                // Si l'insertion échoue, essayer une autre approche
                // Attendre plus longtemps et réessayer
                await new Promise(resolve => setTimeout(resolve, 2000));

                const { error: retryError } = await supabase
                  .from("contact_pharmacies")
                  .insert([contactToInsert]);

                if (retryError) {
                  throw new Error(`Erreur lors de la réinsertion du contact: ${retryError.message}`);
                }
              }

              // Attendre entre chaque insertion
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }
        } catch (error) {
          console.error("Erreur lors de la mise à jour des contacts:", error);
          throw error;
        }
      }

      // 3. Gérer les horaires avec la même approche
      if (horaires) {
        try {
          // 3.1 Supprimer tous les horaires existants
          const { error: deleteError } = await supabase
            .from("horaire_ouverture")
            .delete()
            .eq("id_pharmacie", id);

          if (deleteError) {
            throw new Error(`Erreur lors de la suppression des horaires: ${deleteError.message}`);
          }

          // 3.2 Attendre que la suppression soit complète
          await new Promise(resolve => setTimeout(resolve, 2000));

          // 3.3 Insérer les nouveaux horaires un par un
          if (horaires.length > 0) {
            for (const horaire of horaires) {
              const horaireToInsert = {
                heure_debut: horaire.heure_debut,
                heure_fin: horaire.heure_fin,
                jour: horaire.jour,
                id_pharmacie: id
              };

              // Insérer un seul horaire
              const { error: insertError } = await supabase
                .from("horaire_ouverture")
                .insert([horaireToInsert]);

              if (insertError) {
                console.error("Erreur lors de l'insertion de l'horaire:", insertError);
                throw new Error(`Erreur lors de l'insertion de l'horaire: ${insertError.message}`);
              }

              // Attendre entre chaque insertion
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }
        } catch (error) {
          console.error("Erreur lors de la mise à jour des horaires:", error);
          throw error;
        }
      }

      // 4. Gérer les informations de garde si fournies
      if (gardeData !== undefined) {
        if (gardeData === null) {
          // Si gardeData est null, supprimer toutes les périodes de garde
          const { error: deleteGardeError } = await supabase
            .from("pharmacies_garde")
            .delete()
            .eq("id_pharmacies", id);

          if (deleteGardeError) {
            throw new Error(`Erreur lors de la suppression des périodes de garde: ${deleteGardeError.message}`);
          }
        } else {
          // Sinon, ajouter une nouvelle période de garde
          const { error: gardeError } = await supabase
            .from("pharmacies_garde")
            .insert({
              id_pharmacies: id,
              date_debut: gardeData.date_debut,
              date_fin: gardeData.date_fin
            });

          if (gardeError) {
            throw new Error(`Erreur lors de l'insertion de la période de garde: ${gardeError.message}`);
          }
        }
      }

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la pharmacie:", error);
      throw error;
    }
  },

  /**
   * Supprimer une pharmacie
   */
  deletePharmacyFix: async (id: number) => {
    try {
      console.log("Début de la suppression de la pharmacie avec l'approche de contournement");

      // 1. Supprimer les contacts associés
      const { error: deleteContactsError } = await supabase
        .from("contact_pharmacies")
        .delete()
        .eq("id_pharmacie", id);

      if (deleteContactsError) {
        throw new Error(`Erreur lors de la suppression des contacts: ${deleteContactsError.message}`);
      }

      // Attendre que la suppression soit complète
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 2. Supprimer les horaires associés
      const { error: deleteHorairesError } = await supabase
        .from("horaire_ouverture")
        .delete()
        .eq("id_pharmacie", id);

      if (deleteHorairesError) {
        throw new Error(`Erreur lors de la suppression des horaires: ${deleteHorairesError.message}`);
      }

      // Attendre que la suppression soit complète
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 3. Supprimer les périodes de garde associées
      const { error: deleteGardeError } = await supabase
        .from("pharmacies_garde")
        .delete()
        .eq("id_pharmacies", id);

      if (deleteGardeError) {
        throw new Error(`Erreur lors de la suppression des périodes de garde: ${deleteGardeError.message}`);
      }

      // Attendre que la suppression soit complète
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 4. Supprimer la pharmacie
      const { error: deletePharmacyError } = await supabase
        .from("pharmacies")
        .delete()
        .eq("id", id);

      if (deletePharmacyError) {
        throw new Error(`Erreur lors de la suppression de la pharmacie: ${deletePharmacyError.message}`);
      }

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la suppression de la pharmacie:", error);
      throw error;
    }
  }
};

export default pharmacyServiceFix;
