import supabase from "@/utils/supabase";
import type { PharmacyGarde } from "@/types";

/**
 * Service pour la gestion des pharmacies
 * Version optimisée qui préserve les numéros de contact et évite les suppressions inutiles
 */
export const pharmacyServiceOptimized = {
  /**
   * R<PERSON>cupérer toutes les pharmacies avec leurs contacts, horaires et périodes de garde
   */
  getPharmacies: async () => {
    try {
      // Récupérer les pharmacies
      const { data: pharmacies, error: pharmaciesError } = await supabase
        .from("pharmacies")
        .select("*");

      if (pharmaciesError) throw new Error(pharmaciesError.message);

      // Récupérer les contacts pour toutes les pharmacies
      const { data: contacts, error: contactsError } = await supabase
        .from("contact_pharmacies")
        .select("*");

      if (contactsError) throw new Error(contactsError.message);

      // Récupérer les horaires pour toutes les pharmacies
      const { data: horaires, error: horairesError } = await supabase
        .from("horaire_ouverture")
        .select("*");

      if (horairesError) throw new Error(horairesError.message);

      // Récupérer toutes les informations de garde
      const { data: gardes, error: gardesError } = await supabase
        .from("pharmacies_garde")
        .select("*");

      if (gardesError) throw new Error(gardesError.message);

      // Date actuelle pour vérifier si une pharmacie est de garde
      const now = new Date().toISOString();

      // Associer les contacts, horaires et périodes de garde à chaque pharmacie
      return pharmacies.map((pharmacy) => {
        // Trouver toutes les périodes de garde pour cette pharmacie
        const pharmacyGardes = gardes?.filter(g => g.id_pharmacies === pharmacy.id) || [];

        // Vérifier si la pharmacie est actuellement de garde
        const gardeEnCours = pharmacyGardes.find(
          g => new Date(g.date_debut) <= new Date(now) && new Date(g.date_fin) >= new Date(now)
        );

        // Trier les périodes de garde par date de début (la plus récente d'abord)
        const gardesTriees = [...pharmacyGardes].sort(
          (a, b) => new Date(b.date_debut).getTime() - new Date(a.date_debut).getTime()
        );

        return {
          ...pharmacy,
          contacts: contacts.filter(
            (contact) => contact.id_pharmacie === pharmacy.id,
          ),
          horaires: horaires.filter(
            (horaire) => horaire.id_pharmacie === pharmacy.id,
          ),
          // Ajouter une propriété calculée pour indiquer si la pharmacie est de garde
          de_garde: !!gardeEnCours,
          // Ajouter les informations de garde en cours si disponibles
          garde: gardeEnCours,
          // Ajouter toutes les périodes de garde
          gardes: gardesTriees,
        };
      });
    } catch (error) {
      console.error("Erreur lors de la récupération des pharmacies:", error);
      throw error;
    }
  },

  /**
   * Ajouter une nouvelle pharmacie
   * Cette méthode préserve les numéros de contact et optimise le processus d'ajout
   */
  addPharmacy: async (
    pharmacyData: Partial<Omit<Pharmacy, "id" | "contacts" | "horaires" | "de_garde" | "garde" | "gardes">>,
    contacts: Omit<PharmacyContact, "id" | "id_pharmacie">[],
    horaires: Omit<PharmacySchedule, "id" | "id_pharmacie">[],
    gardeData?: { date_debut: string; date_fin: string }
  ) => {
    try {
      console.log("Début de l'ajout de la pharmacie avec l'approche optimisée");
      
      // 1. Vérifier les données requises
      if (!pharmacyData.nom_pharmacie || !pharmacyData.address) {
        throw new Error("Données manquantes: nom et adresse sont requis");
      }

      if (!pharmacyData.commune) {
        pharmacyData.commune = "Non spécifiée";
      }

      // 2. Supprimer les champs qui ne sont pas dans la base de données
      const { assurance_sante, mutuelle_sante, ...cleanData } = pharmacyData as any;

      // 3. Insérer la pharmacie
      const { data: pharmacy, error: pharmacyError } = await supabase
        .from("pharmacies")
        .insert([cleanData])
        .select()
        .single();

      if (pharmacyError) {
        throw new Error(`Erreur lors de l'insertion de la pharmacie: ${pharmacyError.message}`);
      }

      if (!pharmacy) {
        throw new Error("Aucune pharmacie n'a été créée");
      }

      // 4. Insérer les contacts en une seule opération
      if (contacts && contacts.length > 0) {
        // Filtrer les contacts vides
        const validContacts = contacts.filter(contact => contact.numero.trim() !== "");
        
        if (validContacts.length > 0) {
          // Préparer les contacts pour l'insertion
          const contactsToInsert = validContacts.map(contact => ({
            numero: contact.numero,
            id_pharmacie: pharmacy.id
          }));
          
          // Insérer tous les contacts en une seule opération
          const { error: contactsError } = await supabase
            .from("contact_pharmacies")
            .insert(contactsToInsert);
          
          if (contactsError) {
            console.error("Erreur lors de l'insertion des contacts:", contactsError);
            // Ne pas échouer complètement si l'insertion des contacts échoue
          }
        }
      }

      // 5. Insérer les horaires en une seule opération
      if (horaires && horaires.length > 0) {
        // Filtrer les horaires vides
        const validHoraires = horaires.filter(
          horaire => horaire.heure_debut.trim() !== "" && horaire.heure_fin.trim() !== ""
        );
        
        if (validHoraires.length > 0) {
          // Préparer les horaires pour l'insertion
          const horairesToInsert = validHoraires.map(horaire => ({
            heure_debut: horaire.heure_debut,
            heure_fin: horaire.heure_fin,
            jour: horaire.jour,
            id_pharmacie: pharmacy.id
          }));
          
          // Insérer tous les horaires en une seule opération
          const { error: horairesError } = await supabase
            .from("horaire_ouverture")
            .insert(horairesToInsert);
          
          if (horairesError) {
            console.error("Erreur lors de l'insertion des horaires:", horairesError);
            // Ne pas échouer complètement si l'insertion des horaires échoue
          }
        }
      }

      // 6. Insérer la période de garde si fournie
      let garde: PharmacyGarde | undefined;
      if (gardeData) {
        const { data: gardeResult, error: gardeError } = await supabase
          .from("pharmacies_garde")
          .insert({
            id_pharmacies: pharmacy.id,
            date_debut: gardeData.date_debut,
            date_fin: gardeData.date_fin
          })
          .select()
          .single();

        if (gardeError) {
          console.error("Erreur lors de l'insertion de la période de garde:", gardeError);
          // Ne pas échouer complètement si l'insertion de la période de garde échoue
        } else {
          garde = gardeResult;
        }
      }

      // 7. Récupérer les contacts et horaires insérés pour les retourner
      const { data: insertedContacts, error: getContactsError } = await supabase
        .from("contact_pharmacies")
        .select("*")
        .eq("id_pharmacie", pharmacy.id);
      
      if (getContactsError) {
        console.error("Erreur lors de la récupération des contacts insérés:", getContactsError);
      }
      
      const { data: insertedHoraires, error: getHorairesError } = await supabase
        .from("horaire_ouverture")
        .select("*")
        .eq("id_pharmacie", pharmacy.id);
      
      if (getHorairesError) {
        console.error("Erreur lors de la récupération des horaires insérés:", getHorairesError);
      }

      // 8. Retourner la pharmacie avec les contacts et horaires insérés
      return {
        ...pharmacy,
        contacts: insertedContacts || [],
        horaires: insertedHoraires || [],
        de_garde: !!garde,
        garde: garde
      };
    } catch (error) {
      console.error("Erreur lors de l'ajout de la pharmacie:", error);
      throw error;
    }
  },

  /**
   * Mettre à jour une pharmacie existante
   * Cette méthode préserve les numéros de contact et optimise le processus de mise à jour
   */
  updatePharmacy: async (
    id: number,
    pharmacyData: Partial<Omit<Pharmacy, "id" | "contacts" | "horaires" | "de_garde" | "garde" | "gardes">>,
    contacts?: Omit<PharmacyContact, "id" | "id_pharmacie">[],
    horaires?: Omit<PharmacySchedule, "id" | "id_pharmacie">[],
    gardeData?: { date_debut: string; date_fin: string } | null
  ) => {
    try {
      console.log("Début de la mise à jour de la pharmacie avec l'approche optimisée");
      
      // 1. Supprimer les propriétés calculées qui ne sont pas dans la table
      const { de_garde, garde, gardes, ...dataToUpdate } = pharmacyData as any;
      const { assurance_sante, mutuelle_sante, ...cleanData } = dataToUpdate;
      
      // 2. Préparer les données à envoyer
      const dataToSend = {
        ...cleanData,
        province: cleanData.province || null,
        region: cleanData.region || null,
        district: cleanData.district || null,
        commune: cleanData.commune || "Non spécifiée",
        service: cleanData.service || null,
      };

      // 3. Mettre à jour la pharmacie
      const { error: pharmacyError } = await supabase
        .from("pharmacies")
        .update(dataToSend)
        .eq("id", id);

      if (pharmacyError) {
        throw new Error(`Erreur lors de la mise à jour de la pharmacie: ${pharmacyError.message}`);
      }

      // 4. Mettre à jour les contacts si fournis
      if (contacts) {
        // 4.1 Récupérer les contacts existants
        const { data: existingContacts, error: getContactsError } = await supabase
          .from("contact_pharmacies")
          .select("*")
          .eq("id_pharmacie", id);
        
        if (getContactsError) {
          console.error("Erreur lors de la récupération des contacts existants:", getContactsError);
        } else {
          // 4.2 Filtrer les contacts vides
          const validContacts = contacts.filter(contact => contact.numero.trim() !== "");
          
          // 4.3 Comparer les contacts existants avec les nouveaux contacts
          // Si les contacts sont identiques, ne rien faire
          const existingNumbers = existingContacts?.map(contact => contact.numero) || [];
          const newNumbers = validContacts.map(contact => contact.numero);
          
          // Si les contacts sont différents, mettre à jour
          if (JSON.stringify(existingNumbers.sort()) !== JSON.stringify(newNumbers.sort())) {
            // 4.4 Supprimer les contacts existants
            const { error: deleteContactsError } = await supabase
              .from("contact_pharmacies")
              .delete()
              .eq("id_pharmacie", id);
            
            if (deleteContactsError) {
              console.error("Erreur lors de la suppression des contacts:", deleteContactsError);
            } else if (validContacts.length > 0) {
              // 4.5 Insérer les nouveaux contacts
              const contactsToInsert = validContacts.map(contact => ({
                numero: contact.numero,
                id_pharmacie: id
              }));
              
              const { error: insertContactsError } = await supabase
                .from("contact_pharmacies")
                .insert(contactsToInsert);
              
              if (insertContactsError) {
                console.error("Erreur lors de l'insertion des nouveaux contacts:", insertContactsError);
              }
            }
          }
        }
      }

      // 5. Mettre à jour les horaires si fournis
      if (horaires) {
        // 5.1 Récupérer les horaires existants
        const { data: existingHoraires, error: getHorairesError } = await supabase
          .from("horaire_ouverture")
          .select("*")
          .eq("id_pharmacie", id);
        
        if (getHorairesError) {
          console.error("Erreur lors de la récupération des horaires existants:", getHorairesError);
        } else {
          // 5.2 Filtrer les horaires vides
          const validHoraires = horaires.filter(
            horaire => horaire.heure_debut.trim() !== "" && horaire.heure_fin.trim() !== ""
          );
          
          // 5.3 Comparer les horaires existants avec les nouveaux horaires
          // Si les horaires sont différents, mettre à jour
          const existingHorairesSimplified = existingHoraires?.map(h => ({
            heure_debut: h.heure_debut,
            heure_fin: h.heure_fin,
            jour: h.jour
          })) || [];
          
          const newHorairesSimplified = validHoraires.map(h => ({
            heure_debut: h.heure_debut,
            heure_fin: h.heure_fin,
            jour: h.jour
          }));
          
          // Si les horaires sont différents, mettre à jour
          if (JSON.stringify(existingHorairesSimplified.sort()) !== JSON.stringify(newHorairesSimplified.sort())) {
            // 5.4 Supprimer les horaires existants
            const { error: deleteHorairesError } = await supabase
              .from("horaire_ouverture")
              .delete()
              .eq("id_pharmacie", id);
            
            if (deleteHorairesError) {
              console.error("Erreur lors de la suppression des horaires:", deleteHorairesError);
            } else if (validHoraires.length > 0) {
              // 5.5 Insérer les nouveaux horaires
              const horairesToInsert = validHoraires.map(horaire => ({
                heure_debut: horaire.heure_debut,
                heure_fin: horaire.heure_fin,
                jour: horaire.jour,
                id_pharmacie: id
              }));
              
              const { error: insertHorairesError } = await supabase
                .from("horaire_ouverture")
                .insert(horairesToInsert);
              
              if (insertHorairesError) {
                console.error("Erreur lors de l'insertion des nouveaux horaires:", insertHorairesError);
              }
            }
          }
        }
      }

      // 6. Gérer les informations de garde si fournies
      if (gardeData !== undefined) {
        if (gardeData === null) {
          // Si gardeData est null, supprimer toutes les périodes de garde
          const { error: deleteGardeError } = await supabase
            .from("pharmacies_garde")
            .delete()
            .eq("id_pharmacies", id);

          if (deleteGardeError) {
            console.error("Erreur lors de la suppression des périodes de garde:", deleteGardeError);
          }
        } else {
          // Sinon, ajouter une nouvelle période de garde
          const { error: gardeError } = await supabase
            .from("pharmacies_garde")
            .insert({
              id_pharmacies: id,
              date_debut: gardeData.date_debut,
              date_fin: gardeData.date_fin
            });

          if (gardeError) {
            console.error("Erreur lors de l'insertion de la période de garde:", gardeError);
          }
        }
      }

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la pharmacie:", error);
      throw error;
    }
  },

  /**
   * Supprimer une pharmacie
   */
  deletePharmacy: async (id: number) => {
    try {
      console.log("Début de la suppression de la pharmacie avec l'approche optimisée");
      
      // 1. Supprimer les contacts associés
      try {
        const { error: deleteContactsError } = await supabase
          .from("contact_pharmacies")
          .delete()
          .eq("id_pharmacie", id);

        if (deleteContactsError) {
          console.error("Erreur lors de la suppression des contacts:", deleteContactsError);
          // Continuer malgré l'erreur
        }
      } catch (error) {
        console.error("Erreur lors de la suppression des contacts:", error);
        // Continuer malgré l'erreur
      }

      // 2. Supprimer les horaires associés
      try {
        const { error: deleteHorairesError } = await supabase
          .from("horaire_ouverture")
          .delete()
          .eq("id_pharmacie", id);

        if (deleteHorairesError) {
          console.error("Erreur lors de la suppression des horaires:", deleteHorairesError);
          // Continuer malgré l'erreur
        }
      } catch (error) {
        console.error("Erreur lors de la suppression des horaires:", error);
        // Continuer malgré l'erreur
      }

      // 3. Supprimer les périodes de garde associées
      try {
        const { error: deleteGardeError } = await supabase
          .from("pharmacies_garde")
          .delete()
          .eq("id_pharmacies", id);

        if (deleteGardeError) {
          console.error("Erreur lors de la suppression des périodes de garde:", deleteGardeError);
          // Continuer malgré l'erreur
        }
      } catch (error) {
        console.error("Erreur lors de la suppression des périodes de garde:", error);
        // Continuer malgré l'erreur
      }

      // 4. Supprimer la pharmacie
      const { error: deletePharmacyError } = await supabase
        .from("pharmacies")
        .delete()
        .eq("id", id);

      if (deletePharmacyError) {
        throw new Error(`Erreur lors de la suppression de la pharmacie: ${deletePharmacyError.message}`);
      }

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la suppression de la pharmacie:", error);
      throw error;
    }
  }
};

export default pharmacyServiceOptimized;
