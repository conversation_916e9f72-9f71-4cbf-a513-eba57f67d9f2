import supabase from "@/utils/supabase";
import type { PharmacyGarde } from "@/types";

/**
 * Service pour la gestion des pharmacies
 * Version finale qui garantit l'insertion des numéros de contact
 */
export const pharmacyServiceFinal = {
  /**
   * Récupérer toutes les pharmacies avec leurs contacts, horaires et périodes de garde
   */
  getPharmacies: async () => {
    try {
      console.log("Récupération des pharmacies...");
      
      // Récupérer les pharmacies
      const { data: pharmacies, error: pharmaciesError } = await supabase
        .from("pharmacies")
        .select("*");

      if (pharmaciesError) {
        console.error("Erreur lors de la récupération des pharmacies:", pharmaciesError);
        throw new Error(pharmaciesError.message);
      }

      console.log(`${pharmacies?.length || 0} pharmacies récupérées`);

      // Récupérer les contacts pour toutes les pharmacies
      const { data: contacts, error: contactsError } = await supabase
        .from("contact_pharmacies")
        .select("*");

      if (contactsError) {
        console.error("Erreur lors de la récupération des contacts:", contactsError);
        throw new Error(contactsError.message);
      }

      console.log(`${contacts?.length || 0} contacts récupérés`);

      // Récupérer les horaires pour toutes les pharmacies
      const { data: horaires, error: horairesError } = await supabase
        .from("horaire_ouverture")
        .select("*");

      if (horairesError) {
        console.error("Erreur lors de la récupération des horaires:", horairesError);
        throw new Error(horairesError.message);
      }

      // Récupérer toutes les informations de garde
      const { data: gardes, error: gardesError } = await supabase
        .from("pharmacies_garde")
        .select("*");

      if (gardesError) {
        console.error("Erreur lors de la récupération des gardes:", gardesError);
        throw new Error(gardesError.message);
      }

      // Date actuelle pour vérifier si une pharmacie est de garde
      const now = new Date().toISOString();

      // Associer les contacts, horaires et périodes de garde à chaque pharmacie
      return pharmacies.map((pharmacy) => {
        // Trouver tous les contacts pour cette pharmacie
        const pharmacyContacts = contacts?.filter(
          (contact) => contact.id_pharmacie === pharmacy.id
        ) || [];

        console.log(`Pharmacie ${pharmacy.id} (${pharmacy.nom_pharmacie}) a ${pharmacyContacts.length} contacts`);
        
        // Trouver tous les horaires pour cette pharmacie
        const pharmacyHoraires = horaires?.filter(
          (horaire) => horaire.id_pharmacie === pharmacy.id
        ) || [];

        // Trouver toutes les périodes de garde pour cette pharmacie
        const pharmacyGardes = gardes?.filter(g => g.id_pharmacies === pharmacy.id) || [];

        // Vérifier si la pharmacie est actuellement de garde
        const gardeEnCours = pharmacyGardes.find(
          g => new Date(g.date_debut) <= new Date(now) && new Date(g.date_fin) >= new Date(now)
        );

        // Trier les périodes de garde par date de début (la plus récente d'abord)
        const gardesTriees = [...pharmacyGardes].sort(
          (a, b) => new Date(b.date_debut).getTime() - new Date(a.date_debut).getTime()
        );

        return {
          ...pharmacy,
          contacts: pharmacyContacts,
          horaires: pharmacyHoraires,
          // Ajouter une propriété calculée pour indiquer si la pharmacie est de garde
          de_garde: !!gardeEnCours,
          // Ajouter les informations de garde en cours si disponibles
          garde: gardeEnCours,
          // Ajouter toutes les périodes de garde
          gardes: gardesTriees,
        };
      });
    } catch (error) {
      console.error("Erreur lors de la récupération des pharmacies:", error);
      throw error;
    }
  },

  /**
   * Ajouter une nouvelle pharmacie
   * Cette méthode garantit l'insertion des numéros de contact
   */
  addPharmacy: async (
    pharmacyData: Partial<Omit<Pharmacy, "id" | "contacts" | "horaires" | "de_garde" | "garde" | "gardes">>,
    contacts: { numero: string }[],
    horaires: Omit<PharmacySchedule, "id" | "id_pharmacie">[],
    gardeData?: { date_debut: string; date_fin: string }
  ) => {
    try {
      console.log("Début de l'ajout de la pharmacie avec l'approche finale");
      console.log("Données de la pharmacie:", pharmacyData);
      console.log("Contacts à ajouter:", contacts);
      
      // 1. Vérifier les données requises
      if (!pharmacyData.nom_pharmacie || !pharmacyData.address) {
        throw new Error("Données manquantes: nom et adresse sont requis");
      }

      if (!pharmacyData.commune) {
        pharmacyData.commune = "Non spécifiée";
      }

      // 2. Supprimer les champs qui ne sont pas dans la base de données
      const { assurance_sante, mutuelle_sante, ...cleanData } = pharmacyData as any;

      // 3. Insérer la pharmacie
      const { data: pharmacy, error: pharmacyError } = await supabase
        .from("pharmacies")
        .insert([cleanData])
        .select()
        .single();

      if (pharmacyError) {
        console.error("Erreur lors de l'insertion de la pharmacie:", pharmacyError);
        throw new Error(`Erreur lors de l'insertion de la pharmacie: ${pharmacyError.message}`);
      }

      if (!pharmacy) {
        throw new Error("Aucune pharmacie n'a été créée");
      }

      console.log("Pharmacie créée avec succès:", pharmacy);

      // 4. Insérer les contacts
      const validContacts = contacts.filter(contact => contact.numero.trim() !== "");
      console.log(`${validContacts.length} contacts valides à insérer`);

      if (validContacts.length > 0) {
        // Préparer les contacts pour l'insertion
        const contactsToInsert = validContacts.map(contact => ({
          numero: contact.numero,
          id_pharmacie: pharmacy.id
        }));
        
        console.log("Contacts à insérer:", contactsToInsert);
        
        // Insérer les contacts un par un pour éviter les problèmes
        for (const contact of contactsToInsert) {
          console.log(`Insertion du contact: ${contact.numero}`);
          
          const { data: insertedContact, error: contactError } = await supabase
            .from("contact_pharmacies")
            .insert([contact])
            .select()
            .single();
          
          if (contactError) {
            console.error(`Erreur lors de l'insertion du contact ${contact.numero}:`, contactError);
            // Continuer malgré l'erreur
          } else {
            console.log(`Contact inséré avec succès: ${insertedContact?.numero}`);
          }
          
          // Attendre un peu entre chaque insertion
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 5. Insérer les horaires
      const validHoraires = horaires.filter(
        horaire => horaire.heure_debut.trim() !== "" && horaire.heure_fin.trim() !== ""
      );
      
      if (validHoraires.length > 0) {
        // Préparer les horaires pour l'insertion
        const horairesToInsert = validHoraires.map(horaire => ({
          heure_debut: horaire.heure_debut,
          heure_fin: horaire.heure_fin,
          jour: horaire.jour,
          id_pharmacie: pharmacy.id
        }));
        
        // Insérer les horaires un par un
        for (const horaire of horairesToInsert) {
          const { error: horaireError } = await supabase
            .from("horaire_ouverture")
            .insert([horaire]);
          
          if (horaireError) {
            console.error("Erreur lors de l'insertion de l'horaire:", horaireError);
            // Continuer malgré l'erreur
          }
          
          // Attendre un peu entre chaque insertion
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 6. Insérer la période de garde si fournie
      let garde: PharmacyGarde | undefined;
      if (gardeData) {
        const { data: gardeResult, error: gardeError } = await supabase
          .from("pharmacies_garde")
          .insert({
            id_pharmacies: pharmacy.id,
            date_debut: gardeData.date_debut,
            date_fin: gardeData.date_fin
          })
          .select()
          .single();

        if (gardeError) {
          console.error("Erreur lors de l'insertion de la période de garde:", gardeError);
          // Ne pas échouer complètement si l'insertion de la période de garde échoue
        } else {
          garde = gardeResult;
        }
      }

      // 7. Récupérer les contacts et horaires insérés pour les retourner
      const { data: insertedContacts, error: getContactsError } = await supabase
        .from("contact_pharmacies")
        .select("*")
        .eq("id_pharmacie", pharmacy.id);
      
      if (getContactsError) {
        console.error("Erreur lors de la récupération des contacts insérés:", getContactsError);
      } else {
        console.log(`${insertedContacts?.length || 0} contacts récupérés après insertion`);
      }
      
      const { data: insertedHoraires, error: getHorairesError } = await supabase
        .from("horaire_ouverture")
        .select("*")
        .eq("id_pharmacie", pharmacy.id);
      
      if (getHorairesError) {
        console.error("Erreur lors de la récupération des horaires insérés:", getHorairesError);
      }

      // 8. Retourner la pharmacie avec les contacts et horaires insérés
      return {
        ...pharmacy,
        contacts: insertedContacts || [],
        horaires: insertedHoraires || [],
        de_garde: !!garde,
        garde: garde
      };
    } catch (error) {
      console.error("Erreur lors de l'ajout de la pharmacie:", error);
      throw error;
    }
  },

  /**
   * Mettre à jour une pharmacie existante
   * Cette méthode garantit la préservation des numéros de contact
   */
  updatePharmacy: async (
    id: number,
    pharmacyData: Partial<Omit<Pharmacy, "id" | "contacts" | "horaires" | "de_garde" | "garde" | "gardes">>,
    contacts?: { numero: string }[],
    horaires?: Omit<PharmacySchedule, "id" | "id_pharmacie">[],
    gardeData?: { date_debut: string; date_fin: string } | null
  ) => {
    try {
      console.log("Début de la mise à jour de la pharmacie avec l'approche finale");
      console.log("ID de la pharmacie:", id);
      console.log("Données de la pharmacie:", pharmacyData);
      console.log("Contacts à mettre à jour:", contacts);
      
      // 1. Supprimer les propriétés calculées qui ne sont pas dans la table
      const { de_garde, garde, gardes, ...dataToUpdate } = pharmacyData as any;
      const { assurance_sante, mutuelle_sante, ...cleanData } = dataToUpdate;
      
      // 2. Préparer les données à envoyer
      const dataToSend = {
        ...cleanData,
        province: cleanData.province || null,
        region: cleanData.region || null,
        district: cleanData.district || null,
        commune: cleanData.commune || "Non spécifiée",
        service: cleanData.service || null,
      };

      // 3. Mettre à jour la pharmacie
      const { error: pharmacyError } = await supabase
        .from("pharmacies")
        .update(dataToSend)
        .eq("id", id);

      if (pharmacyError) {
        console.error("Erreur lors de la mise à jour de la pharmacie:", pharmacyError);
        throw new Error(`Erreur lors de la mise à jour de la pharmacie: ${pharmacyError.message}`);
      }

      // 4. Mettre à jour les contacts si fournis
      if (contacts) {
        // 4.1 Récupérer les contacts existants
        const { data: existingContacts, error: getContactsError } = await supabase
          .from("contact_pharmacies")
          .select("*")
          .eq("id_pharmacie", id);
        
        if (getContactsError) {
          console.error("Erreur lors de la récupération des contacts existants:", getContactsError);
        } else {
          console.log("Contacts existants:", existingContacts);
          
          // 4.2 Filtrer les contacts vides
          const validContacts = contacts.filter(contact => contact.numero.trim() !== "");
          console.log("Contacts valides à mettre à jour:", validContacts);
          
          // 4.3 Supprimer tous les contacts existants
          const { error: deleteContactsError } = await supabase
            .from("contact_pharmacies")
            .delete()
            .eq("id_pharmacie", id);
          
          if (deleteContactsError) {
            console.error("Erreur lors de la suppression des contacts:", deleteContactsError);
          } else {
            console.log("Contacts existants supprimés avec succès");
            
            // 4.4 Attendre que la suppression soit complète
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 4.5 Insérer les nouveaux contacts un par un
            if (validContacts.length > 0) {
              for (const contact of validContacts) {
                console.log(`Insertion du contact: ${contact.numero}`);
                
                const contactToInsert = {
                  numero: contact.numero,
                  id_pharmacie: id
                };
                
                const { data: insertedContact, error: insertContactError } = await supabase
                  .from("contact_pharmacies")
                  .insert([contactToInsert])
                  .select()
                  .single();
                
                if (insertContactError) {
                  console.error(`Erreur lors de l'insertion du contact ${contact.numero}:`, insertContactError);
                  // Continuer malgré l'erreur
                } else {
                  console.log(`Contact inséré avec succès: ${insertedContact?.numero}`);
                }
                
                // Attendre un peu entre chaque insertion
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            }
          }
        }
      }

      // 5. Mettre à jour les horaires si fournis
      if (horaires) {
        // 5.1 Filtrer les horaires vides
        const validHoraires = horaires.filter(
          horaire => horaire.heure_debut.trim() !== "" && horaire.heure_fin.trim() !== ""
        );
        
        if (validHoraires.length > 0) {
          // 5.2 Supprimer tous les horaires existants
          const { error: deleteHorairesError } = await supabase
            .from("horaire_ouverture")
            .delete()
            .eq("id_pharmacie", id);
          
          if (deleteHorairesError) {
            console.error("Erreur lors de la suppression des horaires:", deleteHorairesError);
          } else {
            // 5.3 Attendre que la suppression soit complète
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 5.4 Insérer les nouveaux horaires un par un
            for (const horaire of validHoraires) {
              const horaireToInsert = {
                heure_debut: horaire.heure_debut,
                heure_fin: horaire.heure_fin,
                jour: horaire.jour,
                id_pharmacie: id
              };
              
              const { error: insertHoraireError } = await supabase
                .from("horaire_ouverture")
                .insert([horaireToInsert]);
              
              if (insertHoraireError) {
                console.error("Erreur lors de l'insertion de l'horaire:", insertHoraireError);
                // Continuer malgré l'erreur
              }
              
              // Attendre un peu entre chaque insertion
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
        }
      }

      // 6. Gérer les informations de garde si fournies
      if (gardeData !== undefined) {
        if (gardeData === null) {
          // Si gardeData est null, supprimer toutes les périodes de garde
          const { error: deleteGardeError } = await supabase
            .from("pharmacies_garde")
            .delete()
            .eq("id_pharmacies", id);

          if (deleteGardeError) {
            console.error("Erreur lors de la suppression des périodes de garde:", deleteGardeError);
          }
        } else {
          // Sinon, ajouter une nouvelle période de garde
          const { error: gardeError } = await supabase
            .from("pharmacies_garde")
            .insert({
              id_pharmacies: id,
              date_debut: gardeData.date_debut,
              date_fin: gardeData.date_fin
            });

          if (gardeError) {
            console.error("Erreur lors de l'insertion de la période de garde:", gardeError);
          }
        }
      }

      // 7. Récupérer les contacts mis à jour pour vérification
      const { data: updatedContacts, error: getUpdatedContactsError } = await supabase
        .from("contact_pharmacies")
        .select("*")
        .eq("id_pharmacie", id);
      
      if (getUpdatedContactsError) {
        console.error("Erreur lors de la récupération des contacts mis à jour:", getUpdatedContactsError);
      } else {
        console.log(`${updatedContacts?.length || 0} contacts après mise à jour:`, updatedContacts);
      }

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la pharmacie:", error);
      throw error;
    }
  },

  /**
   * Supprimer une pharmacie
   */
  deletePharmacy: async (id: number) => {
    try {
      console.log("Début de la suppression de la pharmacie avec l'approche finale");
      
      // 1. Supprimer les contacts associés
      try {
        const { error: deleteContactsError } = await supabase
          .from("contact_pharmacies")
          .delete()
          .eq("id_pharmacie", id);

        if (deleteContactsError) {
          console.error("Erreur lors de la suppression des contacts:", deleteContactsError);
          // Continuer malgré l'erreur
        }
      } catch (error) {
        console.error("Erreur lors de la suppression des contacts:", error);
        // Continuer malgré l'erreur
      }

      // 2. Supprimer les horaires associés
      try {
        const { error: deleteHorairesError } = await supabase
          .from("horaire_ouverture")
          .delete()
          .eq("id_pharmacie", id);

        if (deleteHorairesError) {
          console.error("Erreur lors de la suppression des horaires:", deleteHorairesError);
          // Continuer malgré l'erreur
        }
      } catch (error) {
        console.error("Erreur lors de la suppression des horaires:", error);
        // Continuer malgré l'erreur
      }

      // 3. Supprimer les périodes de garde associées
      try {
        const { error: deleteGardeError } = await supabase
          .from("pharmacies_garde")
          .delete()
          .eq("id_pharmacies", id);

        if (deleteGardeError) {
          console.error("Erreur lors de la suppression des périodes de garde:", deleteGardeError);
          // Continuer malgré l'erreur
        }
      } catch (error) {
        console.error("Erreur lors de la suppression des périodes de garde:", error);
        // Continuer malgré l'erreur
      }

      // 4. Supprimer la pharmacie
      const { error: deletePharmacyError } = await supabase
        .from("pharmacies")
        .delete()
        .eq("id", id);

      if (deletePharmacyError) {
        throw new Error(`Erreur lors de la suppression de la pharmacie: ${deletePharmacyError.message}`);
      }

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la suppression de la pharmacie:", error);
      throw error;
    }
  }
};

export default pharmacyServiceFinal;
