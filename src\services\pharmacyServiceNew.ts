import supabase from "@/utils/supabase";
import type { PharmacyGarde } from "@/types";

/**
 * Service pour la gestion des pharmacies
 * Version améliorée avec une meilleure gestion des erreurs et des transactions
 */
export const pharmacyService = {
  /**
   * <PERSON><PERSON><PERSON><PERSON><PERSON> toutes les pharmacies avec leurs contacts, horaires et périodes de garde
   */
  getPharmacies: async () => {
    const { data: pharmacies, error: pharmaciesError } = await supabase
      .from("pharmacies")
      .select("*");

    if (pharmaciesError) throw new Error(pharmaciesError.message);

    // Récupérer les contacts pour toutes les pharmacies
    const { data: contacts, error: contactsError } = await supabase
      .from("contact_pharmacies")
      .select("*");

    if (contactsError) throw new Error(contactsError.message);

    // Récupérer les horaires pour toutes les pharmacies
    const { data: horaires, error: horairesError } = await supabase
      .from("horaire_ouverture")
      .select("*");

    if (horairesError) throw new Error(horairesError.message);

    // Récupérer toutes les informations de garde
    const { data: gardes, error: gardesError } = await supabase
      .from("pharmacies_garde")
      .select("*");

    if (gardesError) throw new Error(gardesError.message);

    // Date actuelle pour vérifier si une pharmacie est de garde
    const now = new Date().toISOString();

    // Associer les contacts, horaires et périodes de garde à chaque pharmacie
    return pharmacies.map((pharmacy) => {
      // Trouver toutes les périodes de garde pour cette pharmacie
      const pharmacyGardes = gardes?.filter(g => g.id_pharmacies === pharmacy.id) || [];

      // Vérifier si la pharmacie est actuellement de garde
      const gardeEnCours = pharmacyGardes.find(
        g => new Date(g.date_debut) <= new Date(now) && new Date(g.date_fin) >= new Date(now)
      );

      // Trier les périodes de garde par date de début (la plus récente d'abord)
      const gardesTriees = [...pharmacyGardes].sort(
        (a, b) => new Date(b.date_debut).getTime() - new Date(a.date_debut).getTime()
      );

      return {
        ...pharmacy,
        contacts: contacts.filter(
          (contact) => contact.id_pharmacie === pharmacy.id,
        ),
        horaires: horaires.filter(
          (horaire) => horaire.id_pharmacie === pharmacy.id,
        ),
        // Ajouter une propriété calculée pour indiquer si la pharmacie est de garde
        de_garde: !!gardeEnCours,
        // Ajouter les informations de garde en cours si disponibles
        garde: gardeEnCours,
        // Ajouter toutes les périodes de garde
        gardes: gardesTriees,
      };
    });
  },

  /**
   * Ajouter une nouvelle pharmacie avec ses contacts, horaires et période de garde
   */
  addPharmacy: async (
    pharmacyData: Partial<Omit<Pharmacy, "id" | "contacts" | "horaires" | "de_garde" | "garde" | "gardes">>,
    contacts: Omit<PharmacyContact, "id" | "id_pharmacie">[],
    horaires: Omit<PharmacySchedule, "id" | "id_pharmacie">[],
    gardeData?: { date_debut: string; date_fin: string }
  ) => {
    try {
      // Vérifier les données requises (uniquement nom et adresse)
      if (!pharmacyData.nom_pharmacie || !pharmacyData.address) {
        throw new Error("Données manquantes: nom et adresse sont requis");
      }

      // Fournir une valeur par défaut pour commune si elle n'est pas définie
      if (!pharmacyData.commune) {
        pharmacyData.commune = "Non spécifiée";
      }

      // Supprimer les champs qui ne sont pas dans la base de données
      const { assurance_sante, mutuelle_sante, ...cleanData } = pharmacyData as any;

      // Insérer la pharmacie
      const { data: pharmacy, error: pharmacyError } = await supabase
        .from("pharmacies")
        .insert([cleanData])
        .select()
        .single();

      if (pharmacyError) {
        throw new Error(`Erreur lors de l'insertion de la pharmacie: ${pharmacyError.message}`);
      }

      if (!pharmacy) {
        throw new Error("Aucune pharmacie n'a été créée");
      }

      // Insérer les contacts un par un
      if (contacts.length > 0) {
        try {
          console.log("Insertion des contacts pour la nouvelle pharmacie...");

          for (const contact of contacts) {
            const contactToInsert = {
              numero: contact.numero,
              id_pharmacie: pharmacy.id
            };

            // Insérer un seul contact
            const { error: insertError } = await supabase
              .from("contact_pharmacies")
              .insert([contactToInsert]);

            if (insertError) {
              console.error("Erreur lors de l'insertion du contact:", insertError);
              throw new Error(`Erreur lors de l'insertion du contact: ${insertError.message}`);
            }

            // Attendre entre chaque insertion
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        } catch (error) {
          console.error("Erreur lors de l'insertion des contacts:", error);
          throw error;
        }
      }

      // Insérer les horaires un par un
      if (horaires.length > 0) {
        try {
          console.log("Insertion des horaires pour la nouvelle pharmacie...");

          for (const horaire of horaires) {
            const horaireToInsert = {
              heure_debut: horaire.heure_debut,
              heure_fin: horaire.heure_fin,
              jour: horaire.jour,
              id_pharmacie: pharmacy.id
            };

            // Insérer un seul horaire
            const { error: insertError } = await supabase
              .from("horaire_ouverture")
              .insert([horaireToInsert]);

            if (insertError) {
              console.error("Erreur lors de l'insertion de l'horaire:", insertError);
              throw new Error(`Erreur lors de l'insertion de l'horaire: ${insertError.message}`);
            }

            // Attendre entre chaque insertion
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        } catch (error) {
          console.error("Erreur lors de l'insertion des horaires:", error);
          throw error;
        }
      }

      // Insérer la période de garde si fournie
      let garde: PharmacyGarde | undefined;
      if (gardeData) {
        const { data: gardeResult, error: gardeError } = await supabase
          .from("pharmacies_garde")
          .insert({
            id_pharmacies: pharmacy.id,
            date_debut: gardeData.date_debut,
            date_fin: gardeData.date_fin
          })
          .select()
          .single();

        if (gardeError) {
          throw new Error(`Erreur lors de l'insertion de la période de garde: ${gardeError.message}`);
        }

        garde = gardeResult;
      }

      return {
        ...pharmacy,
        de_garde: !!garde,
        garde: garde
      };
    } catch (error) {
      console.error("Erreur lors de l'ajout de la pharmacie:", error);
      throw error;
    }
  },

  /**
   * Mettre à jour une pharmacie existante
   */
  updatePharmacy: async (
    id: number,
    pharmacyData: Partial<Omit<Pharmacy, "id" | "contacts" | "horaires" | "de_garde" | "garde" | "gardes">>,
    contacts?: Omit<PharmacyContact, "id" | "id_pharmacie">[],
    horaires?: Omit<PharmacySchedule, "id" | "id_pharmacie">[],
    gardeData?: { date_debut: string; date_fin: string } | null
  ) => {
    try {
      // Supprimer les propriétés calculées qui ne sont pas dans la table
      const { de_garde, garde, gardes, ...dataToUpdate } = pharmacyData as any;

      // Supprimer les champs qui ne sont pas dans la base de données
      const { assurance_sante, mutuelle_sante, ...cleanData } = dataToUpdate;

      // Fournir une valeur par défaut pour commune si elle n'est pas définie
      const dataToSend = {
        ...cleanData,
        province: cleanData.province || null,
        region: cleanData.region || null,
        district: cleanData.district || null,
        commune: cleanData.commune || "Non spécifiée",
        service: cleanData.service || null,
      };

      // Mettre à jour la pharmacie
      const { error: pharmacyError } = await supabase
        .from("pharmacies")
        .update(dataToSend)
        .eq("id", id);

      if (pharmacyError) {
        throw new Error(`Erreur lors de la mise à jour de la pharmacie: ${pharmacyError.message}`);
      }

      // Mettre à jour les contacts si fournis
      if (contacts) {
        try {
          // 1. Récupérer les contacts existants pour comprendre la structure
          const { data: existingContacts, error: getContactsError } = await supabase
            .from("contact_pharmacies")
            .select("*")
            .eq("id_pharmacie", id);

          if (getContactsError) {
            throw new Error(`Erreur lors de la récupération des contacts existants: ${getContactsError.message}`);
          }

          console.log("Structure des contacts existants:", existingContacts);

          // 2. Supprimer tous les contacts existants
          const { error: deleteContactsError } = await supabase
            .from("contact_pharmacies")
            .delete()
            .eq("id_pharmacie", id);

          if (deleteContactsError) {
            throw new Error(`Erreur lors de la suppression des contacts: ${deleteContactsError.message}`);
          }

          // 3. Attendre que la suppression soit complète
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 4. Vérifier que les contacts ont bien été supprimés
          const { data: checkContacts, error: checkError } = await supabase
            .from("contact_pharmacies")
            .select("*")
            .eq("id_pharmacie", id);

          if (checkError) {
            throw new Error(`Erreur lors de la vérification de la suppression des contacts: ${checkError.message}`);
          }

          if (checkContacts && checkContacts.length > 0) {
            console.warn("Des contacts existent toujours après la suppression:", checkContacts);
          }

          // 5. Insérer les nouveaux contacts un par un
          if (contacts.length > 0) {
            for (const contact of contacts) {
              const contactToInsert = {
                numero: contact.numero,
                id_pharmacie: id
              };

              // Insérer un seul contact
              const { error: insertError } = await supabase
                .from("contact_pharmacies")
                .insert([contactToInsert]);

              if (insertError) {
                console.error("Erreur lors de l'insertion du contact:", insertError);
                throw new Error(`Erreur lors de l'insertion du contact: ${insertError.message}`);
              }

              // Attendre entre chaque insertion
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
        } catch (error) {
          console.error("Erreur lors de la mise à jour des contacts:", error);
          throw error;
        }
      }

      // Mettre à jour les horaires si fournis
      if (horaires) {
        try {
          // 1. Récupérer les horaires existants pour comprendre la structure
          const { data: existingHoraires, error: getHorairesError } = await supabase
            .from("horaire_ouverture")
            .select("*")
            .eq("id_pharmacie", id);

          if (getHorairesError) {
            throw new Error(`Erreur lors de la récupération des horaires existants: ${getHorairesError.message}`);
          }

          console.log("Structure des horaires existants:", existingHoraires);

          // 2. Supprimer tous les horaires existants
          const { error: deleteHorairesError } = await supabase
            .from("horaire_ouverture")
            .delete()
            .eq("id_pharmacie", id);

          if (deleteHorairesError) {
            throw new Error(`Erreur lors de la suppression des horaires: ${deleteHorairesError.message}`);
          }

          // 3. Attendre que la suppression soit complète
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 4. Vérifier que les horaires ont bien été supprimés
          const { data: checkHoraires, error: checkError } = await supabase
            .from("horaire_ouverture")
            .select("*")
            .eq("id_pharmacie", id);

          if (checkError) {
            throw new Error(`Erreur lors de la vérification de la suppression des horaires: ${checkError.message}`);
          }

          if (checkHoraires && checkHoraires.length > 0) {
            console.warn("Des horaires existent toujours après la suppression:", checkHoraires);
          }

          // 5. Insérer les nouveaux horaires un par un
          if (horaires.length > 0) {
            for (const horaire of horaires) {
              const horaireToInsert = {
                heure_debut: horaire.heure_debut,
                heure_fin: horaire.heure_fin,
                jour: horaire.jour,
                id_pharmacie: id
              };

              // Insérer un seul horaire
              const { error: insertError } = await supabase
                .from("horaire_ouverture")
                .insert([horaireToInsert]);

              if (insertError) {
                console.error("Erreur lors de l'insertion de l'horaire:", insertError);
                throw new Error(`Erreur lors de l'insertion de l'horaire: ${insertError.message}`);
              }

              // Attendre entre chaque insertion
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
        } catch (error) {
          console.error("Erreur lors de la mise à jour des horaires:", error);
          throw error;
        }
      }

      // Gérer les informations de garde si fournies
      if (gardeData !== undefined) {
        if (gardeData === null) {
          // Si gardeData est null, supprimer toutes les périodes de garde
          const { error: deleteGardeError } = await supabase
            .from("pharmacies_garde")
            .delete()
            .eq("id_pharmacies", id);

          if (deleteGardeError) {
            throw new Error(`Erreur lors de la suppression des périodes de garde: ${deleteGardeError.message}`);
          }
        } else {
          // Sinon, ajouter une nouvelle période de garde
          const { error: gardeError } = await supabase
            .from("pharmacies_garde")
            .insert({
              id_pharmacies: id,
              date_debut: gardeData.date_debut,
              date_fin: gardeData.date_fin
            });

          if (gardeError) {
            throw new Error(`Erreur lors de l'insertion de la période de garde: ${gardeError.message}`);
          }
        }
      }

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la pharmacie:", error);
      throw error;
    }
  },

  /**
   * Supprimer une pharmacie
   */
  deletePharmacy: async (id: number) => {
    try {
      // Supprimer les contacts associés
      const { error: deleteContactsError } = await supabase
        .from("contact_pharmacies")
        .delete()
        .eq("id_pharmacie", id);

      if (deleteContactsError) {
        throw new Error(`Erreur lors de la suppression des contacts: ${deleteContactsError.message}`);
      }

      // Supprimer les horaires associés
      const { error: deleteHorairesError } = await supabase
        .from("horaire_ouverture")
        .delete()
        .eq("id_pharmacie", id);

      if (deleteHorairesError) {
        throw new Error(`Erreur lors de la suppression des horaires: ${deleteHorairesError.message}`);
      }

      // Supprimer les périodes de garde associées
      const { error: deleteGardeError } = await supabase
        .from("pharmacies_garde")
        .delete()
        .eq("id_pharmacies", id);

      if (deleteGardeError) {
        throw new Error(`Erreur lors de la suppression des périodes de garde: ${deleteGardeError.message}`);
      }

      // Supprimer la pharmacie
      const { error: deletePharmacyError } = await supabase
        .from("pharmacies")
        .delete()
        .eq("id", id);

      if (deletePharmacyError) {
        throw new Error(`Erreur lors de la suppression de la pharmacie: ${deletePharmacyError.message}`);
      }

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la suppression de la pharmacie:", error);
      throw error;
    }
  },
};

export default pharmacyService;
